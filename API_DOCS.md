# EDU邮箱售卖平台 API 接口文档

## 基础信息

- **基础URL**: `http://localhost:3000/api`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Bearer Token

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {}, // 具体数据
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 错误响应
```json
{
  "success": false,
  "code": 400,
  "message": "错误信息",
  "data": null,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权（token无效或过期） |
| 403 | 禁止访问（权限不足） |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 认证接口

### 1. 用户注册

**接口地址**: `POST /auth/register`

**请求参数**:
```json
{
  "username": "testuser",     // 用户名，3-50字符，只能包含字母和数字
  "email": "<EMAIL>", // 邮箱地址，最多100字符
  "password": "123456"        // 密码，6-50字符
}
```

**成功响应** (201):
```json
{
  "success": true,
  "code": 201,
  "message": "注册成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "role": "customer"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 400: `"用户名只能包含字母和数字"`
- 400: `"用户名或邮箱已存在"`

### 2. 用户登录

**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "testuser",  // 用户名或邮箱
  "password": "123456"     // 密码
}
```

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "role": "customer"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 401: `"用户名或密码错误"`

### 3. Token验证

**接口地址**: `GET /auth/verify`

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "Token验证成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "role": "customer"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 401: `"访问令牌缺失"`
- 401: `"访问令牌已过期"`
- 401: `"访问令牌无效"`

### 4. 用户登出

**接口地址**: `POST /auth/logout`

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "登出成功",
  "data": null,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## 商品接口

### 1. 获取商品列表

**接口地址**: `GET /products`

**请求参数** (Query):
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | number | 否 | 1 | 页码，最小为1 |
| limit | number | 否 | 10 | 每页数量，1-100 |
| status | string | 否 | active | 商品状态：active/inactive/all |
| search | string | 否 | - | 搜索关键词，最多100字符 |
| sort | string | 否 | created_desc | 排序方式：price_asc/price_desc/created_asc/created_desc |

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "获取商品列表成功",
  "data": {
    "products": [
      {
        "id": 1,
        "name": "哈佛大学EDU邮箱",
        "school_logo_url": "http://iph.href.lu/200x200?text=Harvard",
        "description": "哈佛大学官方EDU邮箱，享受全球顶级教育资源...",
        "price": 299.00,
        "warranty_days": 365,
        "status": "active",
        "inventory": {
          "total": 5,
          "available": 4,
          "in_stock": true
        },
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 50,
      "total_pages": 5,
      "has_next": true,
      "has_prev": false
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 2. 获取商品详情

**接口地址**: `GET /products/:id`

**路径参数**:
- `id`: 商品ID

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "获取商品详情成功",
  "data": {
    "product": {
      "id": 1,
      "name": "哈佛大学EDU邮箱",
      "school_logo_url": "http://iph.href.lu/400x400?text=Harvard",
      "description": "哈佛大学官方EDU邮箱，享受全球顶级教育资源和软件优惠...",
      "price": 299.00,
      "warranty_days": 365,
      "status": "active",
      "inventory": {
        "total": 5,
        "available": 4,
        "sold": 1,
        "reserved": 0,
        "in_stock": true
      },
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 400: `"商品ID无效"`
- 404: `"商品不存在"`

### 3. 获取热门商品

**接口地址**: `GET /products/hot`

**请求参数** (Query):
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | number | 否 | 6 | 返回数量，最多20个 |

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "获取热门商品成功",
  "data": {
    "products": [
      {
        "id": 1,
        "name": "哈佛大学EDU邮箱",
        "school_logo_url": "http://iph.href.lu/200x200?text=Harvard",
        "price": 299.00,
        "warranty_days": 365,
        "inventory": {
          "available": 4,
          "in_stock": true
        }
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## 购物车接口

### 1. 添加商品到购物车

**接口地址**: `POST /cart`

**认证要求**: 需要Bearer Token

**请求参数**:
```json
{
  "product_id": 1,        // 商品ID，必填，正整数
  "quantity": 1           // 数量，可选，默认1，范围1-10
}
```

**成功响应** (201/200):
```json
{
  "success": true,
  "code": 201,
  "message": "商品已添加到购物车",
  "data": {
    "cart_item": {
      "id": 1,
      "product_id": 1,
      "product_name": "哈佛大学EDU邮箱",
      "quantity": 1,
      "unit_price": 299.00,
      "total_price": 299.00
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 400: `"商品ID必须是正数"`
- 400: `"商品库存不足"`
- 404: `"商品不存在或已下架"`

### 2. 获取购物车列表

**接口地址**: `GET /cart`

**认证要求**: 需要Bearer Token

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "获取购物车成功",
  "data": {
    "cart_items": [
      {
        "id": 1,
        "product_id": 1,
        "product_name": "哈佛大学EDU邮箱",
        "school_logo_url": "http://iph.href.lu/100x100?text=Harvard",
        "quantity": 2,
        "unit_price": 299.00,
        "total_price": 598.00,
        "product_status": "active",
        "in_stock": true,
        "available_count": 4,
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "summary": {
      "total_items": 1,
      "total_quantity": 2,
      "total_amount": 598.00,
      "valid_items": 1
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 3. 更新购物车商品数量

**接口地址**: `PUT /cart/:id`

**认证要求**: 需要Bearer Token

**路径参数**:
- `id`: 购物车项ID

**请求参数**:
```json
{
  "quantity": 2           // 新数量，必填，范围1-10
}
```

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "购物车商品数量已更新",
  "data": {
    "cart_item": {
      "id": 1,
      "product_id": 1,
      "product_name": "哈佛大学EDU邮箱",
      "quantity": 2,
      "unit_price": 299.00,
      "total_price": 598.00
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 400: `"购物车项ID无效"`
- 400: `"库存不足，当前可用库存：3"`
- 404: `"购物车项不存在"`

### 4. 删除购物车商品

**接口地址**: `DELETE /cart/:id`

**认证要求**: 需要Bearer Token

**路径参数**:
- `id`: 购物车项ID

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "商品已从购物车中删除",
  "data": null,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 400: `"购物车项ID无效"`
- 404: `"购物车项不存在"`

### 5. 清空购物车

**接口地址**: `DELETE /cart`

**认证要求**: 需要Bearer Token

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "购物车已清空",
  "data": {
    "deleted_count": 2
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## 订单接口

### 1. 创建订单

**接口地址**: `POST /orders`

**认证要求**: 需要Bearer Token

**请求参数**:
```json
{
  "receiving_email": "<EMAIL>",  // 接收邮箱，必填，用于接收账号密码
  "payment_method": "mock_pay"            // 支付方式，可选，默认mock_pay
}
```

**成功响应** (201):
```json
{
  "success": true,
  "code": 201,
  "message": "订单创建成功",
  "data": {
    "order": {
      "id": 1,
      "order_number": "E*********************",
      "total_amount": 578.00,
      "receiving_email": "<EMAIL>",
      "payment_method": "mock_pay",
      "status": "pending_payment",
      "items": [
        {
          "product_id": 1,
          "product_name": "哈佛大学EDU邮箱",
          "price": 299.00,
          "quantity": 1,
          "total_price": 299.00
        }
      ]
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 400: `"购物车为空，无法创建订单"`
- 400: `"商品库存不足，当前可用库存：3"`

### 2. 获取订单列表

**接口地址**: `GET /orders`

**认证要求**: 需要Bearer Token

**请求参数** (Query):
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | number | 否 | 1 | 页码，最小为1 |
| limit | number | 否 | 10 | 每页数量，1-50 |

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "获取订单列表成功",
  "data": {
    "orders": [
      {
        "id": 1,
        "order_number": "E*********************",
        "total_amount": 578.00,
        "receiving_email": "<EMAIL>",
        "status": "completed",
        "payment_method": "mock_pay",
        "payment_time": "2024-01-01T00:00:00.000Z",
        "created_at": "2024-01-01T00:00:00.000Z",
        "updated_at": "2024-01-01T00:00:00.000Z",
        "items": [
          {
            "product_id": 1,
            "product_name": "哈佛大学EDU邮箱",
            "price": 299.00,
            "quantity": 1,
            "total_price": 299.00
          }
        ]
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 5,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 3. 获取订单详情

**接口地址**: `GET /orders/:orderNumber`

**认证要求**: 需要Bearer Token

**路径参数**:
- `orderNumber`: 订单号

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "获取订单详情成功",
  "data": {
    "order": {
      "id": 1,
      "order_number": "E*********************",
      "total_amount": 578.00,
      "receiving_email": "<EMAIL>",
      "status": "completed",
      "payment_method": "mock_pay",
      "payment_time": "2024-01-01T00:00:00.000Z",
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z",
      "items": [
        {
          "product_id": 1,
          "product_name": "哈佛大学EDU邮箱",
          "price": 299.00,
          "quantity": 1,
          "total_price": 299.00,
          "delivered_account": {
            "email": "<EMAIL>",
            "password": "HarvardPass123!"
          }
        }
      ]
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**注意**: 只有状态为`completed`的订单才会显示`delivered_account`字段

**错误响应示例**:
- 404: `"订单不存在"`

### 4. 取消订单

**接口地址**: `DELETE /orders/:orderNumber`

**认证要求**: 需要Bearer Token

**路径参数**:
- `orderNumber`: 订单号

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "订单已取消",
  "data": null,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 400: `"只有待支付的订单才能取消"`
- 404: `"订单不存在"`

---

## 支付接口

### 1. 发起支付

**接口地址**: `POST /payment/:orderNumber`

**认证要求**: 需要Bearer Token

**路径参数**:
- `orderNumber`: 订单号

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "支付信息生成成功",
  "data": {
    "payment": {
      "order_number": "E*********************",
      "payment_method": "mock_pay",
      "amount": 578.00,
      "payment_url": "http://localhost:3000/api/payment/mock/E*********************",
      "qr_code": "http://iph.href.lu/200x200?text=扫码支付578元",
      "expires_in": 900
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 400: `"订单状态不正确，无法支付"`
- 404: `"订单不存在"`

### 2. 模拟支付页面

**接口地址**: `GET /payment/mock/:orderNumber`

**认证要求**: 无需认证（公开访问）

**路径参数**:
- `orderNumber`: 订单号

**响应**: HTML页面，包含模拟支付按钮

**用途**: 测试环境下的模拟支付页面，可以模拟支付成功或失败

### 3. 支付回调

**接口地址**: `POST /payment/callback`

**认证要求**: 无需认证（供支付平台回调）

**请求参数**:
```json
{
  "order_number": "E*********************",  // 订单号，必填
  "payment_status": "success",               // 支付状态：success/failed
  "payment_amount": 578.00,                  // 支付金额，必填
  "transaction_id": "MOCK_1752153090123"     // 交易流水号，可选
}
```

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "支付成功，订单已完成",
  "data": null,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**功能说明**:
- 支付成功时会自动更新订单状态为`completed`
- 自动从库存中分配账号密码给订单
- 更新库存状态为`sold`

**错误响应示例**:
- 400: `"支付金额不匹配"`
- 404: `"订单不存在"`

---

## 用户中心接口

### 1. 获取用户个人信息

**接口地址**: `GET /user/profile`

**认证要求**: 需要Bearer Token

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "user": {
      "id": 5,
      "username": "usertest123",
      "email": "<EMAIL>",
      "role": "customer",
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 2. 修改个人信息

**接口地址**: `PUT /user/profile`

**认证要求**: 需要Bearer Token

**请求参数**:
```json
{
  "username": "new_username",     // 新用户名，可选，3-50字符，字母数字下划线
  "email": "<EMAIL>"      // 新邮箱，可选，有效邮箱格式
}
```

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "个人信息更新成功",
  "data": {
    "user": {
      "id": 5,
      "username": "new_username",
      "email": "<EMAIL>",
      "role": "customer",
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 400: `"用户名已被使用"`
- 400: `"邮箱已被使用"`
- 400: `"请提供要更新的信息"`

### 3. 修改密码

**接口地址**: `PUT /user/password`

**认证要求**: 需要Bearer Token

**请求参数**:
```json
{
  "current_password": "oldpass123",    // 当前密码，必填
  "new_password": "newpass123",        // 新密码，必填，6-50字符
  "confirm_password": "newpass123"     // 确认密码，必填，需与新密码一致
}
```

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "密码修改成功",
  "data": null,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应示例**:
- 400: `"当前密码错误"`
- 400: `"新密码不能与当前密码相同"`
- 400: `"确认密码与新密码不一致"`

### 4. 获取用户统计信息

**接口地址**: `GET /user/stats`

**认证要求**: 需要Bearer Token

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "获取用户统计信息成功",
  "data": {
    "stats": {
      "orders": {
        "total": 5,
        "pending": 1,
        "completed": 3,
        "cancelled": 1
      },
      "spending": {
        "total_amount": 1299.00
      },
      "cart": {
        "items_count": 2
      },
      "recent_orders": [
        {
          "order_number": "E*********************",
          "total_amount": 578.00,
          "status": "completed",
          "created_at": "2024-01-01T00:00:00.000Z"
        }
      ]
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 5. 获取用户订单历史

**接口地址**: `GET /user/orders`

**认证要求**: 需要Bearer Token

**请求参数** (Query):
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | number | 否 | 1 | 页码，最小为1 |
| limit | number | 否 | 10 | 每页数量，1-50 |
| status | string | 否 | - | 订单状态筛选：pending_payment/completed/cancelled |

**成功响应** (200):
```json
{
  "success": true,
  "code": 200,
  "message": "获取订单历史成功",
  "data": {
    "orders": [
      {
        "id": 1,
        "order_number": "E*********************",
        "total_amount": 578.00,
        "status": "completed",
        "payment_method": "mock_pay",
        "payment_time": "2024-01-01T00:00:00.000Z",
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 5,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**注意**: 此接口为简化版订单列表，详细订单信息请使用 `/orders/:orderNumber` 接口

---

## 系统接口

### 健康检查

**接口地址**: `GET /health`

**成功响应** (200):
```json
{
  "success": true,
  "message": "EDU邮箱售卖平台API服务正常运行",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## 前端集成说明

### 1. Token存储
- 登录成功后，将返回的 `token` 存储在 localStorage 或 sessionStorage 中
- 建议使用 localStorage 以保持登录状态

### 2. 请求拦截器设置
```javascript
// axios 示例
axios.defaults.baseURL = 'http://localhost:3000/api';

// 请求拦截器 - 自动添加token
axios.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器 - 处理token过期
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // token过期，清除本地存储并跳转到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

### 3. 用户状态管理
```javascript
// 检查登录状态
async function checkAuthStatus() {
  try {
    const response = await axios.get('/auth/verify');
    return response.data.data.user;
  } catch (error) {
    return null;
  }
}

// 登录
async function login(username, password) {
  try {
    const response = await axios.post('/auth/login', { username, password });
    const { user, token } = response.data.data;
    localStorage.setItem('token', token);
    return user;
  } catch (error) {
    throw new Error(error.response?.data?.message || '登录失败');
  }
}

// 注册
async function register(username, email, password) {
  try {
    const response = await axios.post('/auth/register', { username, email, password });
    const { user, token } = response.data.data;
    localStorage.setItem('token', token);
    return user;
  } catch (error) {
    throw new Error(error.response?.data?.message || '注册失败');
  }
}

// 登出
function logout() {
  localStorage.removeItem('token');
  window.location.href = '/login';
}

// 获取商品列表
async function getProducts(params = {}) {
  try {
    const queryString = new URLSearchParams(params).toString();
    const response = await axios.get(`/products?${queryString}`);
    return response.data.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || '获取商品列表失败');
  }
}

// 获取商品详情
async function getProductById(id) {
  try {
    const response = await axios.get(`/products/${id}`);
    return response.data.data.product;
  } catch (error) {
    throw new Error(error.response?.data?.message || '获取商品详情失败');
  }
}

// 获取热门商品
async function getHotProducts(limit = 6) {
  try {
    const response = await axios.get(`/products/hot?limit=${limit}`);
    return response.data.data.products;
  } catch (error) {
    throw new Error(error.response?.data?.message || '获取热门商品失败');
  }
}

// 添加商品到购物车
async function addToCart(productId, quantity = 1) {
  try {
    const response = await axios.post('/cart', { product_id: productId, quantity });
    return response.data.data.cart_item;
  } catch (error) {
    throw new Error(error.response?.data?.message || '添加到购物车失败');
  }
}

// 获取购物车列表
async function getCartItems() {
  try {
    const response = await axios.get('/cart');
    return response.data.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || '获取购物车失败');
  }
}

// 更新购物车商品数量
async function updateCartItem(cartItemId, quantity) {
  try {
    const response = await axios.put(`/cart/${cartItemId}`, { quantity });
    return response.data.data.cart_item;
  } catch (error) {
    throw new Error(error.response?.data?.message || '更新购物车失败');
  }
}

// 删除购物车商品
async function removeCartItem(cartItemId) {
  try {
    await axios.delete(`/cart/${cartItemId}`);
    return true;
  } catch (error) {
    throw new Error(error.response?.data?.message || '删除购物车商品失败');
  }
}

// 清空购物车
async function clearCart() {
  try {
    const response = await axios.delete('/cart');
    return response.data.data.deleted_count;
  } catch (error) {
    throw new Error(error.response?.data?.message || '清空购物车失败');
  }
}

// 创建订单
async function createOrder(receivingEmail, paymentMethod = 'mock_pay') {
  try {
    const response = await axios.post('/orders', {
      receiving_email: receivingEmail,
      payment_method: paymentMethod
    });
    return response.data.data.order;
  } catch (error) {
    throw new Error(error.response?.data?.message || '创建订单失败');
  }
}

// 获取订单列表
async function getOrders(page = 1, limit = 10) {
  try {
    const response = await axios.get(`/orders?page=${page}&limit=${limit}`);
    return response.data.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || '获取订单列表失败');
  }
}

// 获取订单详情
async function getOrderDetail(orderNumber) {
  try {
    const response = await axios.get(`/orders/${orderNumber}`);
    return response.data.data.order;
  } catch (error) {
    throw new Error(error.response?.data?.message || '获取订单详情失败');
  }
}

// 取消订单
async function cancelOrder(orderNumber) {
  try {
    await axios.delete(`/orders/${orderNumber}`);
    return true;
  } catch (error) {
    throw new Error(error.response?.data?.message || '取消订单失败');
  }
}

// 发起支付
async function initiatePayment(orderNumber) {
  try {
    const response = await axios.post(`/payment/${orderNumber}`);
    return response.data.data.payment;
  } catch (error) {
    throw new Error(error.response?.data?.message || '发起支付失败');
  }
}

// 检查订单支付状态（轮询使用）
async function checkOrderStatus(orderNumber) {
  try {
    const order = await getOrderDetail(orderNumber);
    return order.status;
  } catch (error) {
    throw new Error(error.response?.data?.message || '检查订单状态失败');
  }
}

// 获取用户个人信息
async function getUserProfile() {
  try {
    const response = await axios.get('/user/profile');
    return response.data.data.user;
  } catch (error) {
    throw new Error(error.response?.data?.message || '获取用户信息失败');
  }
}

// 修改个人信息
async function updateUserProfile(userData) {
  try {
    const response = await axios.put('/user/profile', userData);
    return response.data.data.user;
  } catch (error) {
    throw new Error(error.response?.data?.message || '修改个人信息失败');
  }
}

// 修改密码
async function changePassword(currentPassword, newPassword, confirmPassword) {
  try {
    await axios.put('/user/password', {
      current_password: currentPassword,
      new_password: newPassword,
      confirm_password: confirmPassword
    });
    return true;
  } catch (error) {
    throw new Error(error.response?.data?.message || '修改密码失败');
  }
}

// 获取用户统计信息
async function getUserStats() {
  try {
    const response = await axios.get('/user/stats');
    return response.data.data.stats;
  } catch (error) {
    throw new Error(error.response?.data?.message || '获取用户统计信息失败');
  }
}

// 获取用户订单历史（简化版）
async function getUserOrderHistory(page = 1, limit = 10, status = null) {
  try {
    let url = `/user/orders?page=${page}&limit=${limit}`;
    if (status) {
      url += `&status=${status}`;
    }
    const response = await axios.get(url);
    return response.data.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || '获取订单历史失败');
  }
}
```

---

---

## 订单状态说明

| 状态 | 说明 |
|------|------|
| pending_payment | 待支付 - 订单已创建，等待用户支付 |
| completed | 已完成 - 支付成功，已自动发货 |
| cancelled | 已取消 - 用户主动取消或系统取消 |

## 业务流程说明

### 完整购买流程
1. **浏览商品** → 用户查看商品列表和详情
2. **添加购物车** → 选择商品加入购物车
3. **创建订单** → 从购物车结算创建订单
4. **发起支付** → 获取支付信息，跳转支付页面
5. **支付回调** → 支付平台回调通知支付结果
6. **自动发货** → 支付成功后自动分配账号密码
7. **查看订单** → 用户查看订单详情获取账号密码

### 自动发货机制
- 支付成功后系统自动从库存中分配可用的邮箱账号
- 每个订单商品都会分配对应数量的账号密码
- 分配后的库存状态会更新为`sold`
- 只有订单状态为`completed`时才能查看账号密码

### 库存管理
- `available`: 可用库存，可以被购买
- `sold`: 已售出，已分配给订单
- `reserved`: 预留库存，暂时不可用

## 注意事项

1. **Token有效期**: 默认7天，过期后需要重新登录
2. **密码安全**: 密码使用bcrypt加密，盐值轮数为12
3. **数据验证**: 所有输入数据都经过严格验证
4. **错误处理**: 统一的错误响应格式，便于前端处理
5. **CORS**: 已配置跨域支持，支持前端开发调试
6. **订单安全**: 用户只能查看和操作自己的订单
7. **库存实时性**: 创建订单时会实时检查库存状态
8. **支付时效**: 模拟支付链接15分钟内有效
9. **自动发货**: 支付成功后立即自动分配账号密码

## 开发环境
- 服务器地址: `http://localhost:3000`
- 数据库: MySQL 8.0
- Node.js版本: 建议16+
