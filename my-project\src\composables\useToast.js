import { ref, createApp } from 'vue'
import Toast from '../components/Toast.vue'

const toasts = ref([])

export function useToast() {
  const showToast = (message, type = 'success', duration = 3000) => {
    const id = Date.now()
    
    // 创建toast容器
    const container = document.createElement('div')
    container.id = `toast-${id}`
    document.body.appendChild(container)
    
    // 创建toast实例
    const app = createApp(Toast, {
      message,
      type,
      duration,
      onClose: () => {
        // 清理DOM
        app.unmount()
        document.body.removeChild(container)
      }
    })
    
    app.mount(container)
    
    return id
  }
  
  const success = (message, duration) => showToast(message, 'success', duration)
  const error = (message, duration) => showToast(message, 'error', duration)
  const warning = (message, duration) => showToast(message, 'warning', duration)
  const info = (message, duration) => showToast(message, 'info', duration)
  
  return {
    showToast,
    success,
    error,
    warning,
    info
  }
}
