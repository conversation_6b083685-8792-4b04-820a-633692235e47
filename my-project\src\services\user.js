import api from './api'

// 用户中心服务
export const userService = {
  // 获取用户个人信息
  async getUserProfile() {
    try {
      const response = await api.get('/user/profile')
      
      if (response.success) {
        return response.data.user
      } else {
        throw new Error(response.message || '获取用户信息失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 修改个人信息
  async updateProfile(profileData) {
    try {
      if (!profileData || (!profileData.username && !profileData.email)) {
        throw new Error('请提供要更新的信息')
      }
      
      const response = await api.put('/user/profile', profileData)
      
      if (response.success) {
        return response.data.user
      } else {
        throw new Error(response.message || '更新个人信息失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 修改密码
  async changePassword(passwordData) {
    try {
      if (!passwordData.current_password || !passwordData.new_password || !passwordData.confirm_password) {
        throw new Error('请填写完整的密码信息')
      }
      
      if (passwordData.new_password !== passwordData.confirm_password) {
        throw new Error('确认密码与新密码不一致')
      }
      
      if (passwordData.new_password.length < 6) {
        throw new Error('新密码长度至少为6位')
      }
      
      const response = await api.put('/user/password', passwordData)
      
      if (response.success) {
        return true
      } else {
        throw new Error(response.message || '修改密码失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 获取用户统计信息
  async getUserStats() {
    try {
      const response = await api.get('/user/stats')
      
      if (response.success) {
        return response.data.stats
      } else {
        throw new Error(response.message || '获取统计信息失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 获取用户订单历史（简化版）
  async getUserOrders(page = 1, limit = 10, status = null) {
    try {
      const params = new URLSearchParams()
      params.append('page', page)
      params.append('limit', limit)
      if (status) {
        params.append('status', status)
      }
      
      const queryString = params.toString()
      const url = `/user/orders?${queryString}`
      
      const response = await api.get(url)
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || '获取订单历史失败')
      }
    } catch (error) {
      throw error
    }
  }
}

export default userService
