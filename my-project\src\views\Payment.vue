<template>
  <div class="payment-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">订单支付</h1>
        <p class="page-subtitle">请完成支付以获取您的EDU邮箱</p>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-section">
        <div class="loading-card">
          <p>正在加载订单信息...</p>
        </div>
      </div>
      
      <!-- 支付内容 -->
      <div v-else-if="order" class="payment-content">
        <!-- 订单信息 -->
        <div class="order-info-section">
          <div class="order-info-card">
            <div class="card-header">
              <h2>订单信息</h2>
              <div class="order-status" :class="`status-${order.status}`">
                {{ getStatusText(order.status) }}
              </div>
            </div>
            
            <div class="order-details">
              <div class="detail-row">
                <span class="label">订单号:</span>
                <span class="value">{{ order.order_number }}</span>
              </div>
              <div class="detail-row">
                <span class="label">订单金额:</span>
                <span class="value amount">¥{{ order.total_amount }}</span>
              </div>
              <div class="detail-row">
                <span class="label">接收邮箱:</span>
                <span class="value">{{ order.receiving_email }}</span>
              </div>
              <div class="detail-row">
                <span class="label">支付方式:</span>
                <span class="value">{{ getPaymentMethodText(order.payment_method) }}</span>
              </div>
              <div class="detail-row">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatDate(order.created_at) }}</span>
              </div>
            </div>
            
            <div class="order-items">
              <h3>商品清单</h3>
              <div class="items-list">
                <div
                  v-for="item in order.items"
                  :key="item.product_id"
                  class="order-item"
                >
                  <span class="item-name">{{ item.product_name }}</span>
                  <span class="item-quantity">x{{ item.quantity }}</span>
                  <span class="item-price">¥{{ item.total_price }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 支付区域 -->
        <div class="payment-section">
          <!-- 待支付状态 -->
          <div v-if="order.status === 'pending_payment'" class="payment-card">
            <div class="card-header">
              <h2>支付方式</h2>
            </div>
            
            <div class="payment-methods">
              <div class="payment-method active">
                <div class="method-icon">💳</div>
                <div class="method-info">
                  <h3>模拟支付</h3>
                  <p>测试环境虚拟支付</p>
                </div>
              </div>
            </div>
            
            <div class="payment-amount">
              <span class="amount-label">支付金额:</span>
              <span class="amount-value">¥{{ order.total_amount }}</span>
            </div>
            
            <div class="payment-actions">
              <button
                @click="handlePayment"
                :disabled="isProcessing"
                class="btn btn-primary pay-btn"
              >
                {{ isProcessing ? '处理中...' : '立即支付' }}
              </button>
              <button
                @click="cancelOrder"
                :disabled="isProcessing"
                class="btn cancel-btn"
              >
                取消订单
              </button>
            </div>
            
            <div class="payment-tips">
              <h4>支付说明:</h4>
              <ul>
                <li>这是测试环境，使用虚拟支付</li>
                <li>支付成功后将自动分配EDU邮箱账号</li>
                <li>账号密码将发送到您的接收邮箱</li>
                <li>如有问题请联系客服</li>
              </ul>
            </div>
          </div>
          
          <!-- 支付成功状态 -->
          <div v-else-if="order.status === 'completed'" class="success-card">
            <div class="success-icon">✅</div>
            <h2>支付成功！</h2>
            <p>您的订单已完成，EDU邮箱账号已自动分配</p>
            <div class="success-actions">
              <router-link :to="`/orders/${order.order_number}`" class="btn btn-primary">
                查看订单详情
              </router-link>
              <router-link to="/orders" class="btn btn-accent">
                我的订单
              </router-link>
            </div>
          </div>
          
          <!-- 已取消状态 -->
          <div v-else-if="order.status === 'cancelled'" class="cancelled-card">
            <div class="cancelled-icon">❌</div>
            <h2>订单已取消</h2>
            <p>该订单已被取消，如需购买请重新下单</p>
            <div class="cancelled-actions">
              <router-link to="/products" class="btn btn-primary">
                继续购物
              </router-link>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else class="error-section">
        <div class="error-card">
          <h3>订单不存在</h3>
          <p>{{ errorMessage || '您访问的订单不存在或已被删除' }}</p>
          <router-link to="/orders" class="btn btn-primary">查看我的订单</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from '../composables/useToast'
import { useGlobalState } from '../composables/useGlobalState'
import { orderService } from '../services/order'
import { paymentService } from '../services/payment'

export default {
  name: 'Payment',
  props: {
    orderNumber: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const { success, error } = useToast()
    const { refreshUserCenterData } = useGlobalState()
    
    // 响应式数据
    const order = ref(null)
    const isLoading = ref(false)
    const isProcessing = ref(false)
    const errorMessage = ref('')
    const statusCheckInterval = ref(null)
    
    // 方法
    const loadOrder = async () => {
      isLoading.value = true
      errorMessage.value = ''
      
      try {
        const orderData = await orderService.getOrderDetail(props.orderNumber)
        order.value = orderData
      } catch (err) {
        errorMessage.value = err.message || '加载订单失败'
        error(err.message || '加载订单失败')
      } finally {
        isLoading.value = false
      }
    }
    
    const handlePayment = async () => {
      if (!order.value || order.value.status !== 'pending_payment') {
        error('订单状态不正确，无法支付')
        return
      }
      
      isProcessing.value = true
      try {
        // 模拟支付处理
        await paymentService.mockPayment(props.orderNumber, 'success')
        
        success('支付成功！正在处理订单...')
        
        // 开始轮询检查订单状态
        startStatusCheck()
        
      } catch (err) {
        error(err.message || '支付失败')
        isProcessing.value = false
      }
    }
    
    const cancelOrder = async () => {
      if (!confirm('确定要取消这个订单吗？')) return
      
      isProcessing.value = true
      try {
        await orderService.cancelOrder(props.orderNumber)
        success('订单已取消')
        
        // 重新加载订单状态
        await loadOrder()
        
      } catch (err) {
        error(err.message || '取消订单失败')
      } finally {
        isProcessing.value = false
      }
    }
    
    const startStatusCheck = () => {
      statusCheckInterval.value = setInterval(async () => {
        try {
          const status = await paymentService.checkOrderStatus(props.orderNumber)
          if (status === 'completed') {
            clearInterval(statusCheckInterval.value)
            await loadOrder()
            isProcessing.value = false
            success('订单处理完成！')
            // 刷新用户中心数据以更新统计信息
            await refreshUserCenterData()
          } else if (status === 'cancelled') {
            clearInterval(statusCheckInterval.value)
            await loadOrder()
            isProcessing.value = false
          }
        } catch (err) {
          console.error('检查订单状态失败:', err)
        }
      }, 2000) // 每2秒检查一次
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        'pending_payment': '待支付',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    }
    
    const getPaymentMethodText = (method) => {
      const methodMap = {
        'mock_pay': '模拟支付'
      }
      return methodMap[method] || method
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
    
    // 组件挂载时加载订单
    onMounted(() => {
      loadOrder()
    })
    
    // 组件卸载时清理定时器
    onUnmounted(() => {
      if (statusCheckInterval.value) {
        clearInterval(statusCheckInterval.value)
      }
    })
    
    return {
      order,
      isLoading,
      isProcessing,
      errorMessage,
      loadOrder,
      handlePayment,
      cancelOrder,
      getStatusText,
      getPaymentMethodText,
      formatDate
    }
  }
}
</script>

<style scoped>
.payment-page {
  min-height: calc(100vh - 80px);
  padding: var(--spacing-lg) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
}

.loading-section,
.error-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-card,
.error-card {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  max-width: 400px;
}

.payment-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: var(--spacing-xl);
}

.order-info-card,
.payment-card,
.success-card,
.cancelled-card {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--secondary-surface);
  border-bottom: var(--border-width) solid var(--neutral-black);
}

.card-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.order-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  border: var(--border-width) solid var(--neutral-black);
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.status-pending_payment {
  background-color: #fff3cd;
  color: #856404;
}

.status-completed {
  background-color: #d4edda;
  color: #155724;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.order-details {
  padding: var(--spacing-lg);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid #eee;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .label {
  color: #666;
  font-weight: var(--font-weight-medium);
}

.detail-row .value {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-black);
}

.detail-row .value.amount {
  color: var(--accent-highlight);
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
}

.order-items {
  padding: var(--spacing-lg);
  border-top: var(--border-width) solid var(--neutral-black);
  background-color: #f8f9fa;
}

.order-items h3 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 1.2rem;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.order-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: var(--spacing-md);
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--neutral-white);
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius);
}

.item-name {
  font-weight: var(--font-weight-medium);
}

.item-quantity {
  color: #666;
  font-size: 0.9rem;
}

.item-price {
  font-weight: var(--font-weight-bold);
  color: var(--accent-highlight);
}

.payment-section {
  align-self: start;
  position: sticky;
  top: var(--spacing-lg);
}

.payment-methods {
  padding: var(--spacing-lg);
}

.payment-method {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  background-color: var(--neutral-white);
  cursor: pointer;
}

.payment-method.active {
  background-color: var(--secondary-surface);
}

.method-icon {
  font-size: 2rem;
}

.method-info h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.1rem;
}

.method-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.payment-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--secondary-surface);
  border-top: var(--border-width) solid var(--neutral-black);
  border-bottom: var(--border-width) solid var(--neutral-black);
}

.amount-label {
  font-size: 1.2rem;
  font-weight: var(--font-weight-medium);
}

.amount-value {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--accent-highlight);
}

.payment-actions {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.pay-btn {
  padding: var(--spacing-md);
  font-size: 1.2rem;
}

.cancel-btn {
  padding: var(--spacing-sm);
  background-color: #f8d7da;
  color: #721c24;
  border: var(--border-width) solid #721c24;
}

.cancel-btn:hover {
  background-color: #f5c6cb;
}

.pay-btn:disabled,
.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.pay-btn:disabled:hover,
.cancel-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.payment-tips {
  padding: var(--spacing-lg);
  background-color: #f8f9fa;
  border-top: var(--border-width) solid var(--neutral-black);
}

.payment-tips h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1rem;
}

.payment-tips ul {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.payment-tips li {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: var(--spacing-xs);
}

.success-card,
.cancelled-card {
  padding: var(--spacing-xl);
  text-align: center;
}

.success-icon,
.cancelled-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
}

.success-card h2,
.cancelled-card h2 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 1.8rem;
}

.success-card p,
.cancelled-card p {
  margin: 0 0 var(--spacing-xl) 0;
  color: #666;
  font-size: 1.1rem;
}

.success-actions,
.cancelled-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .payment-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .payment-section {
    position: static;
  }
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .order-item {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .payment-amount {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .page-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .success-actions,
  .cancelled-actions {
    flex-direction: column;
  }
}
</style>
