<template>
  <div id="app">
    <nav class="navbar">
      <div class="nav-brand">
        <router-link to="/" class="brand-link">EDU邮箱平台</router-link>
      </div>
      <div class="nav-links">
        <router-link to="/" class="nav-link">首页</router-link>
        <router-link to="/login" class="nav-link">登录</router-link>
        <router-link to="/register" class="nav-link">注册</router-link>
      </div>
    </nav>
    <main class="main-content">
      <router-view />
    </main>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
/* 基础样式将在下一步实现 */
#app {
  min-height: 100vh;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: white;
  border-bottom: 2px solid black;
}

.nav-links {
  display: flex;
  gap: 1rem;
}

.nav-link, .brand-link {
  text-decoration: none;
  color: black;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border: 2px solid black;
  border-radius: 8px;
  background: white;
  transition: all 0.2s;
}

.nav-link:hover, .brand-link:hover {
  transform: translate(-2px, -2px);
  box-shadow: 4px 4px 0 black;
}

.main-content {
  padding: 2rem;
}
</style>
