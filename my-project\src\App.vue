<template>
  <div id="app">
    <nav class="navbar">
      <div class="nav-brand">
        <router-link to="/" class="brand-link">EDU邮箱平台</router-link>
      </div>
      <div class="nav-links">
        <router-link to="/" class="nav-link">首页</router-link>
        <router-link to="/products" class="nav-link">商品</router-link>
        <template v-if="isAuthenticated">
          <router-link to="/cart" class="nav-link cart-link">
            <span class="cart-icon">🛒</span>
            <span v-if="cartCount > 0" class="cart-badge">{{ cartCount }}</span>
            购物车
          </router-link>
          <router-link to="/orders" class="nav-link">我的订单</router-link>
          <router-link to="/user" class="nav-link">用户中心</router-link>
          <span class="nav-user">欢迎，{{ user?.username }}</span>
          <button @click="handleLogout" class="nav-link logout-btn">登出</button>
        </template>
        <template v-else>
          <router-link to="/login" class="nav-link">登录</router-link>
          <router-link to="/register" class="nav-link">注册</router-link>
        </template>
      </div>
    </nav>
    <main class="main-content">
      <router-view />
    </main>
  </div>
</template>

<script>
import { useAuth } from './composables/useAuth'
import { useCart } from './composables/useCart'
import { useUserCenter } from './composables/useUserCenter'
import { useRouter } from 'vue-router'
import { onMounted, watch } from 'vue'

export default {
  name: 'App',
  setup() {
    const router = useRouter()
    const { user, isAuthenticated, logout } = useAuth()
    const { cartCount, loadCart } = useCart()
    const { refreshUserData, clearUserData } = useUserCenter()

    const handleLogout = async () => {
      try {
        await logout()
        clearUserData() // 清除用户中心数据
        router.push('/')
      } catch (error) {
        console.error('登出失败:', error)
      }
    }

    // 监听登录状态变化，加载购物车和用户数据
    watch(isAuthenticated, (newValue) => {
      if (newValue) {
        loadCart()
        refreshUserData()
      } else {
        clearUserData()
      }
    })

    // 组件挂载时加载购物车和用户数据
    onMounted(() => {
      if (isAuthenticated.value) {
        loadCart()
        refreshUserData()
      }
    })

    return {
      user,
      isAuthenticated,
      cartCount,
      handleLogout
    }
  }
}
</script>

<style>
/* 基础样式将在下一步实现 */
#app {
  min-height: 100vh;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: white;
  border-bottom: 2px solid black;
}

.nav-links {
  display: flex;
  gap: 1rem;
}

.nav-link, .brand-link {
  text-decoration: none;
  color: black;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border: 2px solid black;
  border-radius: 8px;
  background: white;
  transition: all 0.2s;
}

.nav-link:hover, .brand-link:hover {
  transform: translate(-2px, -2px);
  box-shadow: 4px 4px 0 black;
}

.nav-user {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: black;
  padding: 0.5rem 1rem;
  background: var(--secondary-surface);
  border: 2px solid black;
  border-radius: 8px;
}

.logout-btn {
  background: none;
  border: 2px solid black;
  cursor: pointer;
  font-family: inherit;
}

.logout-btn:hover {
  background: #ffebee;
  color: #c62828;
}

.cart-link {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.cart-icon {
  font-size: 1.2rem;
}

.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--accent-highlight);
  color: var(--neutral-white);
  font-size: 0.7rem;
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid var(--neutral-black);
  min-width: 18px;
  text-align: center;
  line-height: 1;
}

.main-content {
  padding: 2rem;
}
</style>
