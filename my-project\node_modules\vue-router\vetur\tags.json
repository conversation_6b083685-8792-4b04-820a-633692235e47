{"router-view": {"attributes": ["name", "route"], "description": "Component that renders the matched component for the current location. Components rendered by `<router-view>` can also contain their own `<router-view>` to render nested routes."}, "router-link": {"attributes": ["to", "replace", "custom", "active-class", "exact-active-class", "aria-current-value"], "description": "Component that renders an `<a>` with the correct `href` attribute and click listeners to trigger a local navigation when clicked. Can also customize its rendering by providing the `custom` prop and using its `v-slot` API."}}