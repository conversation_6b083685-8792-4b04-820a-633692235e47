import { useAuth } from './useAuth'
import { useCart } from './useCart'
import { useUserCenter } from './useUserCenter'

// 全局状态管理
export function useGlobalState() {
  const { isAuthenticated, reloadUser } = useAuth()
  const { loadCart } = useCart()
  const { refreshUserData } = useUserCenter()
  
  // 刷新所有用户相关数据
  const refreshAllUserData = async () => {
    if (!isAuthenticated.value) return
    
    try {
      await Promise.all([
        reloadUser(),
        loadCart(),
        refreshUserData()
      ])
    } catch (error) {
      console.error('刷新用户数据失败:', error)
    }
  }
  
  // 刷新购物车数据
  const refreshCartData = async () => {
    if (!isAuthenticated.value) return
    
    try {
      await loadCart()
    } catch (error) {
      console.error('刷新购物车数据失败:', error)
    }
  }
  
  // 刷新用户中心数据
  const refreshUserCenterData = async () => {
    if (!isAuthenticated.value) return
    
    try {
      await refreshUserData()
    } catch (error) {
      console.error('刷新用户中心数据失败:', error)
    }
  }
  
  // 清除所有用户数据
  const clearAllUserData = () => {
    const { clearUserData } = useUserCenter()
    clearUserData()
  }
  
  return {
    refreshAllUserData,
    refreshCartData,
    refreshUserCenterData,
    clearAllUserData
  }
}
