<template>
  <div class="user-profile-page">
    <div class="container">
      <!-- 返回按钮 -->
      <div class="back-section">
        <button @click="goBack" class="btn back-btn">
          ← 返回用户中心
        </button>
      </div>
      
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">编辑个人资料</h1>
        <p class="page-subtitle">修改您的基本信息</p>
      </div>
      
      <!-- 未登录提示 -->
      <div v-if="!isAuthenticated" class="login-prompt-section">
        <div class="login-prompt-card">
          <h3>请先登录</h3>
          <p>登录后才能编辑个人资料</p>
          <div class="login-actions">
            <router-link to="/login" class="btn btn-primary">登录</router-link>
            <router-link to="/register" class="btn btn-accent">注册</router-link>
          </div>
        </div>
      </div>
      
      <!-- 编辑表单 -->
      <div v-else class="profile-form-section">
        <div class="profile-form-card">
          <div class="card-header">
            <h2>基本信息</h2>
          </div>
          
          <form @submit.prevent="handleUpdateProfile" class="profile-form">
            <div class="form-group">
              <label for="username" class="form-label">用户名 *</label>
              <input
                id="username"
                v-model="profileForm.username"
                type="text"
                class="input-field"
                placeholder="请输入用户名"
                required
                :disabled="isUpdating"
              />
              <p class="form-help">用户名只能包含字母和数字，3-50个字符</p>
            </div>
            
            <div class="form-group">
              <label for="email" class="form-label">邮箱地址 *</label>
              <input
                id="email"
                v-model="profileForm.email"
                type="email"
                class="input-field"
                placeholder="请输入邮箱地址"
                required
                :disabled="isUpdating"
              />
              <p class="form-help">邮箱地址用于接收重要通知</p>
            </div>
            
            <div class="form-actions">
              <button
                type="button"
                @click="resetForm"
                class="btn reset-btn"
                :disabled="isUpdating"
              >
                重置
              </button>
              <button
                type="submit"
                class="btn btn-primary update-btn"
                :disabled="!isFormValid || isUpdating"
              >
                {{ isUpdating ? '保存中...' : '保存修改' }}
              </button>
            </div>
          </form>
          
          <!-- 错误提示 -->
          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
          
          <!-- 成功提示 -->
          <div v-if="successMessage" class="success-message">
            {{ successMessage }}
          </div>
        </div>
        
        <!-- 账户信息 -->
        <div class="account-info-card">
          <div class="card-header">
            <h2>账户信息</h2>
          </div>
          
          <div class="account-details">
            <div class="detail-row">
              <span class="label">用户ID:</span>
              <span class="value">{{ user?.id || '未知' }}</span>
            </div>
            <div class="detail-row">
              <span class="label">账户类型:</span>
              <span class="value">{{ getRoleText(user?.role) }}</span>
            </div>
            <div class="detail-row">
              <span class="label">注册时间:</span>
              <span class="value">{{ formatDate(userProfile?.created_at) }}</span>
            </div>
            <div class="detail-row">
              <span class="label">最后更新:</span>
              <span class="value">{{ formatDate(userProfile?.updated_at) }}</span>
            </div>
          </div>
          
          <div class="security-actions">
            <router-link to="/user/password" class="btn btn-accent">
              修改密码
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { useUserCenter } from '../composables/useUserCenter'
import { useToast } from '../composables/useToast'

export default {
  name: 'UserProfile',
  setup() {
    const router = useRouter()
    const { isAuthenticated, user } = useAuth()
    const {
      userProfile,
      loadUserProfile,
      updateUserProfile
    } = useUserCenter()
    const { success, error } = useToast()
    
    // 响应式数据
    const profileForm = ref({
      username: '',
      email: ''
    })
    
    const isUpdating = ref(false)
    const errorMessage = ref('')
    const successMessage = ref('')
    
    // 计算属性
    const isFormValid = computed(() => {
      return (
        profileForm.value.username &&
        profileForm.value.email &&
        profileForm.value.username.length >= 3 &&
        profileForm.value.username.length <= 50 &&
        /^[a-zA-Z0-9]+$/.test(profileForm.value.username) &&
        /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profileForm.value.email)
      )
    })
    
    // 方法
    const goBack = () => {
      router.push('/user')
    }
    
    const initializeForm = () => {
      if (user.value) {
        profileForm.value = {
          username: user.value.username || '',
          email: user.value.email || ''
        }
      }
    }
    
    const resetForm = () => {
      initializeForm()
      errorMessage.value = ''
      successMessage.value = ''
    }
    
    const handleUpdateProfile = async () => {
      if (!isFormValid.value) {
        errorMessage.value = '请填写正确的信息'
        return
      }
      
      isUpdating.value = true
      errorMessage.value = ''
      successMessage.value = ''
      
      try {
        await updateUserProfile({
          username: profileForm.value.username,
          email: profileForm.value.email
        })
        
        successMessage.value = '个人资料更新成功！'
        success('个人资料更新成功！')
        
        // 3秒后清除成功消息
        setTimeout(() => {
          successMessage.value = ''
        }, 3000)
        
      } catch (err) {
        errorMessage.value = err.message || '更新失败，请重试'
        error(err.message || '更新失败，请重试')
      } finally {
        isUpdating.value = false
      }
    }
    
    const getRoleText = (role) => {
      const roleMap = {
        'customer': '普通用户',
        'admin': '管理员'
      }
      return roleMap[role] || '用户'
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
    
    // 监听用户信息变化
    watch(user, () => {
      initializeForm()
    }, { immediate: true })
    
    // 组件挂载时加载数据
    onMounted(() => {
      if (isAuthenticated.value) {
        loadUserProfile()
        initializeForm()
      }
    })
    
    return {
      isAuthenticated,
      user,
      userProfile,
      profileForm,
      isUpdating,
      errorMessage,
      successMessage,
      isFormValid,
      goBack,
      resetForm,
      handleUpdateProfile,
      getRoleText,
      formatDate
    }
  }
}
</script>

<style scoped>
.user-profile-page {
  min-height: calc(100vh - 80px);
  padding: var(--spacing-lg) 0;
}

.back-section {
  margin-bottom: var(--spacing-lg);
}

.back-btn {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
}

.login-prompt-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.login-prompt-card {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  max-width: 400px;
}

.login-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-lg);
}

.profile-form-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
  max-width: 1000px;
  margin: 0 auto;
}

.profile-form-card,
.account-info-card {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-lg);
  background-color: var(--secondary-surface);
  border-bottom: var(--border-width) solid var(--neutral-black);
}

.card-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.profile-form {
  padding: var(--spacing-lg);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  font-size: 0.9rem;
}

.form-help {
  font-size: 0.8rem;
  color: #666;
  margin: var(--spacing-xs) 0 0 0;
  line-height: 1.4;
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

.reset-btn {
  flex: 1;
  padding: var(--spacing-md);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
}

.update-btn {
  flex: 2;
  padding: var(--spacing-md);
  font-size: 1.1rem;
}

.update-btn:disabled,
.reset-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.update-btn:disabled:hover,
.reset-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: var(--spacing-sm);
  border: var(--border-width) solid #c62828;
  border-radius: var(--border-radius);
  margin-top: var(--spacing-md);
  font-size: 0.9rem;
  text-align: center;
}

.success-message {
  background-color: #e8f5e8;
  color: #2e7d32;
  padding: var(--spacing-sm);
  border: var(--border-width) solid #2e7d32;
  border-radius: var(--border-radius);
  margin-top: var(--spacing-md);
  font-size: 0.9rem;
  text-align: center;
}

.account-details {
  padding: var(--spacing-lg);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid #eee;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .label {
  color: #666;
  font-weight: var(--font-weight-medium);
}

.detail-row .value {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-black);
}

.security-actions {
  padding: var(--spacing-lg);
  border-top: var(--border-width) solid var(--neutral-black);
  background-color: #f8f9fa;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .profile-form-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .login-actions {
    flex-direction: column;
  }

  .page-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .profile-form-section {
    margin: 0 var(--spacing-md);
  }
}
</style>
