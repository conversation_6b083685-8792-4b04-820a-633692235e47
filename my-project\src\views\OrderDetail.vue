<template>
  <div class="order-detail-page">
    <div class="container">
      <!-- 返回按钮 -->
      <div class="back-section">
        <button @click="goBack" class="btn back-btn">
          ← 返回订单列表
        </button>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-section">
        <div class="loading-card">
          <p>正在加载订单详情...</p>
        </div>
      </div>
      
      <!-- 订单详情 -->
      <div v-else-if="order" class="order-detail">
        <!-- 订单基本信息 -->
        <div class="order-info-section">
          <div class="order-info-card">
            <div class="card-header">
              <h1>订单详情</h1>
              <div class="order-status" :class="`status-${order.status}`">
                {{ getStatusText(order.status) }}
              </div>
            </div>
            
            <div class="order-details">
              <div class="detail-row">
                <span class="label">订单号:</span>
                <span class="value">{{ order.order_number }}</span>
              </div>
              <div class="detail-row">
                <span class="label">订单金额:</span>
                <span class="value amount">¥{{ order.total_amount }}</span>
              </div>
              <div class="detail-row">
                <span class="label">接收邮箱:</span>
                <span class="value">{{ order.receiving_email }}</span>
              </div>
              <div class="detail-row">
                <span class="label">支付方式:</span>
                <span class="value">{{ getPaymentMethodText(order.payment_method) }}</span>
              </div>
              <div class="detail-row">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatDate(order.created_at) }}</span>
              </div>
              <div v-if="order.payment_time" class="detail-row">
                <span class="label">支付时间:</span>
                <span class="value">{{ formatDate(order.payment_time) }}</span>
              </div>
            </div>
            
            <div v-if="order.status === 'pending_payment'" class="order-actions">
              <router-link
                :to="`/payment/${order.order_number}`"
                class="btn btn-primary"
              >
                去支付
              </router-link>
              <button
                @click="cancelOrder"
                class="btn cancel-btn"
              >
                取消订单
              </button>
            </div>
          </div>
        </div>
        
        <!-- 商品列表 -->
        <div class="order-items-section">
          <div class="items-card">
            <div class="card-header">
              <h2>商品清单</h2>
            </div>
            
            <div class="items-list">
              <div
                v-for="item in order.items"
                :key="item.product_id"
                class="order-item"
              >
                <div class="item-info">
                  <h3 class="item-name">{{ item.product_name }}</h3>
                  <p class="item-details">
                    单价: ¥{{ item.price }} × {{ item.quantity }}
                  </p>
                </div>
                <div class="item-total">
                  <span class="total-price">¥{{ item.total_price }}</span>
                </div>
                
                <!-- 已交付的账号信息 -->
                <div v-if="item.delivered_account && order.status === 'completed'" class="delivered-account">
                  <h4>已交付账号:</h4>
                  <div class="account-info">
                    <div class="account-field">
                      <label>邮箱:</label>
                      <div class="account-value">
                        <span>{{ item.delivered_account.email }}</span>
                        <button @click="copyToClipboard(item.delivered_account.email)" class="btn copy-btn">
                          复制
                        </button>
                      </div>
                    </div>
                    <div class="account-field">
                      <label>密码:</label>
                      <div class="account-value">
                        <span :class="{ 'password-hidden': !showPassword }">
                          {{ showPassword ? item.delivered_account.password : '••••••••' }}
                        </span>
                        <button @click="togglePassword" class="btn toggle-btn">
                          {{ showPassword ? '隐藏' : '显示' }}
                        </button>
                        <button @click="copyToClipboard(item.delivered_account.password)" class="btn copy-btn">
                          复制
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="account-tips">
                    <p>⚠️ 请妥善保管您的账号密码，建议立即修改密码</p>
                    <p>📧 账号密码已发送到您的接收邮箱: {{ order.receiving_email }}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="items-summary">
              <div class="summary-row">
                <span class="label">商品总额:</span>
                <span class="value">¥{{ order.total_amount }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else class="error-section">
        <div class="error-card">
          <h3>订单不存在</h3>
          <p>{{ errorMessage || '您访问的订单不存在或已被删除' }}</p>
          <router-link to="/orders" class="btn btn-primary">返回订单列表</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from '../composables/useToast'
import { useGlobalState } from '../composables/useGlobalState'
import { orderService } from '../services/order'

export default {
  name: 'OrderDetail',
  props: {
    orderNumber: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const { success, error } = useToast()
    const { refreshUserCenterData } = useGlobalState()
    
    // 响应式数据
    const order = ref(null)
    const isLoading = ref(false)
    const errorMessage = ref('')
    const showPassword = ref(false)
    
    // 方法
    const loadOrder = async () => {
      isLoading.value = true
      errorMessage.value = ''
      
      try {
        const orderData = await orderService.getOrderDetail(props.orderNumber)
        order.value = orderData
      } catch (err) {
        errorMessage.value = err.message || '加载订单详情失败'
        error(err.message || '加载订单详情失败')
      } finally {
        isLoading.value = false
      }
    }
    
    const goBack = () => {
      router.push('/orders')
    }
    
    const cancelOrder = async () => {
      if (!confirm('确定要取消这个订单吗？')) return
      
      try {
        await orderService.cancelOrder(props.orderNumber)
        success('订单已取消')
        // 重新加载订单详情
        await loadOrder()
        // 刷新用户中心数据以更新统计信息
        await refreshUserCenterData()
      } catch (err) {
        error(err.message || '取消订单失败')
      }
    }
    
    const togglePassword = () => {
      showPassword.value = !showPassword.value
    }
    
    const copyToClipboard = async (text) => {
      try {
        await navigator.clipboard.writeText(text)
        success('已复制到剪贴板')
      } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        success('已复制到剪贴板')
      }
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        'pending_payment': '待支付',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    }
    
    const getPaymentMethodText = (method) => {
      const methodMap = {
        'mock_pay': '模拟支付'
      }
      return methodMap[method] || method
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
    
    // 组件挂载时加载订单
    onMounted(() => {
      loadOrder()
    })
    
    return {
      order,
      isLoading,
      errorMessage,
      showPassword,
      loadOrder,
      goBack,
      cancelOrder,
      togglePassword,
      copyToClipboard,
      getStatusText,
      getPaymentMethodText,
      formatDate
    }
  }
}
</script>

<style scoped>
.order-detail-page {
  min-height: calc(100vh - 80px);
  padding: var(--spacing-lg) 0;
}

.back-section {
  margin-bottom: var(--spacing-lg);
}

.back-btn {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
}

.loading-section,
.error-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-card,
.error-card {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  max-width: 400px;
}

.order-detail {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  max-width: 800px;
  margin: 0 auto;
}

.order-info-card,
.items-card {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--secondary-surface);
  border-bottom: var(--border-width) solid var(--neutral-black);
}

.card-header h1,
.card-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.order-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  border: var(--border-width) solid var(--neutral-black);
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.status-pending_payment {
  background-color: #fff3cd;
  color: #856404;
}

.status-completed {
  background-color: #d4edda;
  color: #155724;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.order-details {
  padding: var(--spacing-lg);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid #eee;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .label {
  color: #666;
  font-weight: var(--font-weight-medium);
}

.detail-row .value {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-black);
}

.detail-row .value.amount {
  color: var(--accent-highlight);
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
}

.order-actions {
  padding: var(--spacing-lg);
  border-top: var(--border-width) solid var(--neutral-black);
  background-color: #f8f9fa;
  display: flex;
  gap: var(--spacing-md);
}

.cancel-btn {
  background-color: #f8d7da;
  color: #721c24;
  border: var(--border-width) solid #721c24;
}

.cancel-btn:hover {
  background-color: #f5c6cb;
}

.items-list {
  padding: var(--spacing-lg);
}

.order-item {
  padding: var(--spacing-lg);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  background-color: var(--neutral-white);
}

.order-item:last-child {
  margin-bottom: 0;
}

.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.item-name {
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
  margin: 0;
}

.item-details {
  font-size: 0.9rem;
  color: #666;
  margin: var(--spacing-xs) 0 0 0;
}

.item-total {
  text-align: right;
}

.total-price {
  font-size: 1.3rem;
  font-weight: var(--font-weight-bold);
  color: var(--accent-highlight);
}

.delivered-account {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg);
  background-color: var(--secondary-surface);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
}

.delivered-account h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 1.1rem;
  color: var(--neutral-black);
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.account-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.account-field label {
  font-weight: var(--font-weight-medium);
  color: #666;
  font-size: 0.9rem;
}

.account-value {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.account-value span {
  flex: 1;
  padding: var(--spacing-sm);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  font-family: monospace;
  font-size: 0.9rem;
}

.password-hidden {
  font-family: inherit !important;
  letter-spacing: 2px;
}

.copy-btn,
.toggle-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.8rem;
  white-space: nowrap;
}

.copy-btn {
  background-color: var(--accent-highlight);
  color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
}

.toggle-btn {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
}

.account-tips {
  background-color: #fff3cd;
  border: var(--border-width) solid #ffc107;
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
}

.account-tips p {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 0.9rem;
  color: #856404;
}

.account-tips p:last-child {
  margin-bottom: 0;
}

.items-summary {
  padding: var(--spacing-lg);
  border-top: var(--border-width) solid var(--neutral-black);
  background-color: #f8f9fa;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
}

.summary-row .value {
  color: var(--accent-highlight);
  font-size: 1.4rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .item-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .item-total {
    text-align: left;
  }

  .order-actions {
    flex-direction: column;
  }

  .account-value {
    flex-direction: column;
    align-items: stretch;
  }

  .account-value span {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .order-detail {
    margin: 0 var(--spacing-md);
  }

  .copy-btn,
  .toggle-btn {
    font-size: 0.7rem;
    padding: 4px 8px;
  }
}
</style>
