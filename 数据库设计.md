以下是我的设计方案，包含了核心实体识别、表结构设计、关系说明、完整的SQL创建语句以及性能优化建议。

-----

### 1\. 核心数据实体识别

根据您的功能清单，我识别出以下六个核心业务实体：
数据库名称edu_db
1.  **用户 (Users):** 存储网站的注册用户信息，包括其角色和基本资料。
2.  **商品 (Products):** 定义了在售的EDU邮箱商品类别，如“某某大学邮箱”，包含其通用描述、价格等。
3.  **商品库存 (Product Inventory):** 存储每一个具体的、可供出售的EDU邮箱账号和密码。这是实现自动发货的核心。
4.  **购物车 (Shopping Cart Items):** 记录用户加入购物车的商品信息。
5.  **订单 (Orders):** 用户下单时创建的主记录，包含订单号、总金额、状态等。
6.  **订单详情 (Order Items):** 记录一个订单中具体包含了哪些商品，以及成功支付后，具体发货的是哪一个库存账号。

-----

### 2\. 数据表结构设计

我将为每个实体设计一张数据表，并遵循最佳实践。

#### 2.1 用户表 (users)

存储系统中的所有用户，并通过 `role` 字段区分角色。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `INT UNSIGNED` | `PRIMARY KEY`, `AUTO_INCREMENT` | 用户唯一ID |
| `username` | `VARCHAR(50)` | `NOT NULL`, `UNIQUE` | 用户名，用于登录 |
| `password_hash` | `VARCHAR(255)` | `NOT NULL` | 加密后的密码哈希 |
| `email` | `VARCHAR(100)` | `NOT NULL`, `UNIQUE` | 注册邮箱，用于登录和联系 |
| `role` | `ENUM('customer', 'admin')` | `NOT NULL`, `DEFAULT 'customer'` | 用户角色 (customer: 注册用户, admin: 管理员) |
| `created_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP` | 账号创建时间 |
| `updated_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 账号信息更新时间 |

#### 2.2 商品表 (products)

定义在售的商品类型。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `INT UNSIGNED` | `PRIMARY KEY`, `AUTO_INCREMENT` | 商品唯一ID |
| `name` | `VARCHAR(100)` | `NOT NULL` | 商品名称 (如: 牛津大学EDU邮箱) |
| `school_logo_url` | `VARCHAR(255)` | `NULL` | 学校Logo图片地址 |
| `description` | `TEXT` | `NULL` | 商品详细描述 (包含权益、注意事项等) |
| `price` | `DECIMAL(10, 2)` | `NOT NULL` | 商品售价 |
| `warranty_days` | `INT` | `NOT NULL`, `DEFAULT 0` | 质保天数 (0表示无质保) |
| `status` | `ENUM('active', 'inactive')` | `NOT NULL`, `DEFAULT 'active'` | 商品状态 (active: 在售, inactive: 已下架) |
| `created_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP` | 商品创建时间 |
| `updated_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 商品信息更新时间 |

#### 2.3 商品库存表 (product\_inventory)

管理具体的邮箱账号库存，是自动发货功能的数据基础。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | `PRIMARY KEY`, `AUTO_INCREMENT` | 库存唯一ID |
| `product_id` | `INT UNSIGNED` | `NOT NULL` | 关联的商品ID (外键) |
| `email_account` | `VARCHAR(255)` | `NOT NULL` | EDU邮箱账号 (建议在后端加密存储) |
| `email_password`| `VARCHAR(255)` | `NOT NULL` | EDU邮箱密码 (建议在后端加密存储) |
| `status` | `ENUM('available', 'sold', 'reserved')` | `NOT NULL`, `DEFAULT 'available'` | 库存状态 (available: 可用, sold: 已售, reserved: 预留) |
| `created_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP` | 入库时间 |
| `updated_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 状态更新时间 |

#### 2.4 购物车项表 (shopping\_cart\_items)

存储每个用户购物车中的商品。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | `PRIMARY KEY`, `AUTO_INCREMENT` | 购物车项唯一ID |
| `user_id` | `INT UNSIGNED` | `NOT NULL` | 用户ID (外键) |
| `product_id` | `INT UNSIGNED` | `NOT NULL` | 商品ID (外键) |
| `quantity` | `INT UNSIGNED` | `NOT NULL`, `DEFAULT 1` | 购买数量 (此业务中通常为1) |
| `created_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP` | 添加时间 |
| `updated_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 更新时间 |

#### 2.5 订单表 (orders)

记录用户的购买订单信息。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | `PRIMARY KEY`, `AUTO_INCREMENT` | 订单唯一ID |
| `order_number` | `VARCHAR(32)` | `NOT NULL`, `UNIQUE` | 业务订单号 (由后端生成，便于查询) |
| `user_id` | `INT UNSIGNED` | `NOT NULL` | 下单用户ID (外键) |
| `total_amount` | `DECIMAL(10, 2)` | `NOT NULL` | 订单总金额 |
| `receiving_email`| `VARCHAR(100)`| `NOT NULL` | 用于接收商品账号密码的邮箱 |
| `status` | `ENUM('pending_payment', 'completed', 'cancelled')` | `NOT NULL`, `DEFAULT 'pending_payment'` | 订单状态 |
| `payment_method`| `VARCHAR(50)` | `NULL` | 支付方式 (如: alipay, wechat\_pay) |
| `payment_time` | `TIMESTAMP` | `NULL` | 支付成功时间 |
| `created_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP` | 下单时间 |
| `updated_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 订单更新时间 |

#### 2.6 订单详情表 (order\_items)

记录订单与商品、库存的详细关系。这是用户查看已购账号密码的核心。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | `PRIMARY KEY`, `AUTO_INCREMENT` | 订单详情唯一ID |
| `order_id` | `BIGINT UNSIGNED` | `NOT NULL` | 订单ID (外键) |
| `product_id` | `INT UNSIGNED` | `NOT NULL` | 商品ID (外键) |
| `inventory_id` | `BIGINT UNSIGNED`| `NULL`, `UNIQUE` | **发货的库存ID (外键)，支付成功后回填** |
| `product_name` | `VARCHAR(100)`| `NOT NULL` | 商品名称快照，防止商品信息变更 |
| `price` | `DECIMAL(10, 2)`| `NOT NULL` | 成交价格快照 |
| `quantity` | `INT UNSIGNED` | `NOT NULL`, `DEFAULT 1` | 购买数量 |

-----

### 3\. 表关系说明

  * **一对多 (One-to-Many):**

      * `users` (1) -\> `orders` (N): 一个用户可以有多个订单。
      * `users` (1) -\> `shopping_cart_items` (N): 一个用户购物车里可以有多个商品项。
      * `products` (1) -\> `product_inventory` (N): 一种商品类型可以有多个具体的库存账号。
      * `products` (1) -\> `shopping_cart_items` (N): 一个商品可以被添加到多个购物车项中。
      * `orders` (1) -\> `order_items` (N): 一个订单可以包含多个购买的商品项。

  * **一对一 (One-to-One):**

      * `product_inventory` (1) -\> `order_items` (1): 一个已售出的库存账号，只对应一个订单详情项。通过在 `order_items.inventory_id` 上设置`UNIQUE`约束实现。

-----

### 4\. 完整的 CREATE TABLE SQL

以下是可直接在MySQL中执行的完整SQL代码，已包含表结构、主键、外键和约束。

```sql
-- ----------------------------
-- 使用 InnoDB 引擎并设置默认字符集
-- ----------------------------
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户唯一ID',
  `username` varchar(50) NOT NULL COMMENT '用户名，用于登录',
  `password_hash` varchar(255) NOT NULL COMMENT '加密后的密码哈希',
  `email` varchar(100) NOT NULL COMMENT '注册邮箱，用于登录和联系',
  `role` enum('customer','admin') NOT NULL DEFAULT 'customer' COMMENT '用户角色 (customer: 注册用户, admin: 管理员)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '账号创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '账号信息更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '商品唯一ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称 (如: 牛津大学EDU邮箱)',
  `school_logo_url` varchar(255) DEFAULT NULL COMMENT '学校Logo图片地址',
  `description` text COMMENT '商品详细描述 (包含权益、注意事项等)',
  `price` decimal(10,2) NOT NULL COMMENT '商品售价',
  `warranty_days` int(11) NOT NULL DEFAULT '0' COMMENT '质保天数 (0表示无质保)',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '商品状态 (active: 在售, inactive: 已下架)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '商品创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '商品信息更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- ----------------------------
-- Table structure for product_inventory
-- ----------------------------
DROP TABLE IF EXISTS `product_inventory`;
CREATE TABLE `product_inventory` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '库存唯一ID',
  `product_id` int(10) unsigned NOT NULL COMMENT '关联的商品ID (外键)',
  `email_account` varchar(255) NOT NULL COMMENT 'EDU邮箱账号 (建议在后端加密存储)',
  `email_password` varchar(255) NOT NULL COMMENT 'EDU邮箱密码 (建议在后端加密存储)',
  `status` enum('available','sold','reserved') NOT NULL DEFAULT 'available' COMMENT '库存状态 (available: 可用, sold: 已售, reserved: 预留)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '状态更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id_status` (`product_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品库存表';

-- ----------------------------
-- Table structure for shopping_cart_items
-- ----------------------------
DROP TABLE IF EXISTS `shopping_cart_items`;
CREATE TABLE `shopping_cart_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '购物车项唯一ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID (外键)',
  `product_id` int(10) unsigned NOT NULL COMMENT '商品ID (外键)',
  `quantity` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '购买数量 (此业务中通常为1)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`user_id`,`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车项表';

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单唯一ID',
  `order_number` varchar(32) NOT NULL COMMENT '业务订单号 (由后端生成，便于查询)',
  `user_id` int(10) unsigned NOT NULL COMMENT '下单用户ID (外键)',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `receiving_email` varchar(100) NOT NULL COMMENT '用于接收商品账号密码的邮箱',
  `status` enum('pending_payment','completed','cancelled') NOT NULL DEFAULT 'pending_payment' COMMENT '订单状态',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式 (如: alipay, wechat_pay)',
  `payment_time` timestamp NULL DEFAULT NULL COMMENT '支付成功时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '订单更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_number` (`order_number`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- ----------------------------
-- Table structure for order_items
-- ----------------------------
DROP TABLE IF EXISTS `order_items`;
CREATE TABLE `order_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单详情唯一ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID (外键)',
  `product_id` int(10) unsigned NOT NULL COMMENT '商品ID (外键)',
  `inventory_id` bigint(20) unsigned DEFAULT NULL COMMENT '发货的库存ID (外键)，支付成功后回填',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称快照，防止商品信息变更',
  `price` decimal(10,2) NOT NULL COMMENT '成交价格快照',
  `quantity` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '购买数量',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_inventory_id` (`inventory_id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单详情表';

SET FOREIGN_KEY_CHECKS = 1;
```

-----

### 5\. 性能优化与索引建议

索引是提升数据库查询性能的关键。除了主键和唯一键自带的索引外，我已在 `CREATE TABLE` 语句中加入了一些关键的普通索引 (`KEY`/`INDEX`)。这里做进一步说明：

1.  **`users` 表:**

      * `uk_username` 和 `uk_email` 这两个唯一索引至关重要，能极大地加速登录验证（通过用户名或邮箱查找用户）的过程。

2.  **`product_inventory` 表:**

      * **`idx_product_id_status` (`product_id`, `status`):** 这是一个复合索引，也是本业务的核心性能保障。当用户下单，系统需要为某个商品（`product_id`）查找一个“可用”（`status = 'available'`）的库存时，该索引可以极速定位，避免全表扫描。

3.  **`shopping_cart_items` 表:**

      * **`uk_user_product` (`user_id`, `product_id`):** 这个唯一复合索引有两个作用：一是确保一个用户对同一个商品在购物车里只有一条记录；二是可以快速查询指定用户的购物车内容。

4.  **`orders` 表:**

      * **`idx_user_id` (`user_id`):** 关键索引。用于在“用户中心”快速查询某个用户的所有历史订单。

5.  **`order_items` 表:**

      * **`idx_order_id` (`order_id`):** 关键索引。当用户查看订单详情时，可以通过订单ID快速找到该订单下的所有商品项。
      * **`uk_inventory_id` (`inventory_id`):** 唯一索引，确保一个库存账号不会被错误地卖给多个订单，保证数据一致性。
