<template>
  <div class="orders-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">我的订单</h1>
        <p class="page-subtitle">查看您的购买记录</p>
      </div>
      
      <!-- 未登录提示 -->
      <div v-if="!isAuthenticated" class="login-prompt-section">
        <div class="login-prompt-card">
          <h3>请先登录</h3>
          <p>登录后即可查看您的订单</p>
          <div class="login-actions">
            <router-link to="/login" class="btn btn-primary">登录</router-link>
            <router-link to="/register" class="btn btn-accent">注册</router-link>
          </div>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-else-if="isLoading" class="loading-section">
        <div class="loading-card">
          <p>正在加载订单...</p>
        </div>
      </div>
      
      <!-- 订单列表 -->
      <div v-else-if="orders.length > 0" class="orders-content">
        <div class="orders-list">
          <div
            v-for="order in orders"
            :key="order.id"
            class="order-card"
            @click="goToOrderDetail(order.order_number)"
          >
            <div class="order-header">
              <div class="order-info">
                <h3 class="order-number">订单号: {{ order.order_number }}</h3>
                <p class="order-date">{{ formatDate(order.created_at) }}</p>
              </div>
              <div class="order-status" :class="`status-${order.status}`">
                {{ getStatusText(order.status) }}
              </div>
            </div>
            
            <div class="order-items">
              <div
                v-for="item in order.items"
                :key="item.product_id"
                class="order-item"
              >
                <span class="item-name">{{ item.product_name }}</span>
                <span class="item-quantity">x{{ item.quantity }}</span>
                <span class="item-price">¥{{ item.total_price }}</span>
              </div>
            </div>
            
            <div class="order-footer">
              <div class="order-total">
                <span class="total-label">订单总额:</span>
                <span class="total-amount">¥{{ order.total_amount }}</span>
              </div>
              <div class="order-actions">
                <button
                  v-if="order.status === 'pending_payment'"
                  @click.stop="goToPayment(order.order_number)"
                  class="btn btn-primary action-btn"
                >
                  去支付
                </button>
                <button
                  v-if="order.status === 'completed'"
                  @click.stop="goToOrderDetail(order.order_number)"
                  class="btn btn-accent action-btn"
                >
                  查看详情
                </button>
                <button
                  v-if="order.status === 'pending_payment'"
                  @click.stop="cancelOrder(order.order_number)"
                  class="btn cancel-btn action-btn"
                >
                  取消订单
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分页 -->
        <div v-if="pagination && pagination.total_pages > 1" class="pagination-section">
          <div class="pagination">
            <button
              @click="goToPage(pagination.current_page - 1)"
              :disabled="!pagination.has_prev"
              class="btn pagination-btn"
            >
              上一页
            </button>
            
            <span class="pagination-info">
              第 {{ pagination.current_page }} 页，共 {{ pagination.total_pages }} 页
            </span>
            
            <button
              @click="goToPage(pagination.current_page + 1)"
              :disabled="!pagination.has_next"
              class="btn pagination-btn"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-orders-section">
        <div class="empty-orders-card">
          <h3>暂无订单</h3>
          <p>您还没有任何订单，快去购买EDU邮箱吧！</p>
          <router-link to="/products" class="btn btn-primary">
            去购物
          </router-link>
        </div>
      </div>
      
      <!-- 错误提示 -->
      <div v-if="errorMessage" class="error-section">
        <div class="error-card">
          <p>{{ errorMessage }}</p>
          <button @click="loadOrders" class="btn btn-primary">重试</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { useToast } from '../composables/useToast'
import { useGlobalState } from '../composables/useGlobalState'
import { orderService } from '../services/order'

export default {
  name: 'Orders',
  setup() {
    const router = useRouter()
    const { isAuthenticated } = useAuth()
    const { success, error } = useToast()
    const { refreshUserCenterData } = useGlobalState()
    
    // 响应式数据
    const orders = ref([])
    const pagination = ref(null)
    const isLoading = ref(false)
    const errorMessage = ref('')
    const currentPage = ref(1)
    
    // 方法
    const loadOrders = async (page = 1) => {
      if (!isAuthenticated.value) return
      
      isLoading.value = true
      errorMessage.value = ''
      
      try {
        const response = await orderService.getOrders(page, 10)
        orders.value = response.orders
        pagination.value = response.pagination
        currentPage.value = page
      } catch (err) {
        errorMessage.value = err.message || '加载订单失败'
        error(err.message || '加载订单失败')
      } finally {
        isLoading.value = false
      }
    }
    
    const goToPage = (page) => {
      if (page >= 1 && page <= pagination.value.total_pages) {
        loadOrders(page)
      }
    }
    
    const goToOrderDetail = (orderNumber) => {
      router.push(`/orders/${orderNumber}`)
    }
    
    const goToPayment = (orderNumber) => {
      router.push(`/payment/${orderNumber}`)
    }
    
    const cancelOrder = async (orderNumber) => {
      if (!confirm('确定要取消这个订单吗？')) return
      
      try {
        await orderService.cancelOrder(orderNumber)
        success('订单已取消')
        // 重新加载订单列表
        loadOrders(currentPage.value)
        // 刷新用户中心数据以更新统计信息
        await refreshUserCenterData()
      } catch (err) {
        error(err.message || '取消订单失败')
      }
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        'pending_payment': '待支付',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
    
    // 组件挂载时加载数据
    onMounted(() => {
      if (isAuthenticated.value) {
        loadOrders(1)
      }
    })
    
    return {
      isAuthenticated,
      orders,
      pagination,
      isLoading,
      errorMessage,
      currentPage,
      loadOrders,
      goToPage,
      goToOrderDetail,
      goToPayment,
      cancelOrder,
      getStatusText,
      formatDate
    }
  }
}
</script>

<style scoped>
.orders-page {
  min-height: calc(100vh - 80px);
  padding: var(--spacing-lg) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
}

.login-prompt-section,
.loading-section,
.empty-orders-section,
.error-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.login-prompt-card,
.loading-card,
.empty-orders-card,
.error-card {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  max-width: 400px;
}

.login-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-lg);
}

.orders-content {
  max-width: 800px;
  margin: 0 auto;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.order-card {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.order-card:hover {
  transform: translate(-2px, -2px);
  box-shadow: calc(var(--shadow-offset) + 2px) calc(var(--shadow-offset) + 2px) 0 var(--neutral-black);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--secondary-surface);
  border-bottom: var(--border-width) solid var(--neutral-black);
}

.order-info h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
}

.order-date {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.order-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  border: var(--border-width) solid var(--neutral-black);
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.status-pending_payment {
  background-color: #fff3cd;
  color: #856404;
}

.status-completed {
  background-color: #d4edda;
  color: #155724;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.order-items {
  padding: var(--spacing-lg);
}

.order-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: var(--spacing-md);
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid #eee;
}

.order-item:last-child {
  border-bottom: none;
}

.item-name {
  font-weight: var(--font-weight-medium);
}

.item-quantity {
  color: #666;
  font-size: 0.9rem;
}

.item-price {
  font-weight: var(--font-weight-bold);
  color: var(--accent-highlight);
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: #f8f9fa;
  border-top: var(--border-width) solid var(--neutral-black);
}

.order-total {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.total-label {
  font-weight: var(--font-weight-medium);
  color: #666;
}

.total-amount {
  font-size: 1.3rem;
  font-weight: var(--font-weight-bold);
  color: var(--accent-highlight);
}

.order-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-btn {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 0.9rem;
  white-space: nowrap;
}

.cancel-btn {
  background-color: #f8d7da;
  color: #721c24;
  border: var(--border-width) solid #721c24;
}

.cancel-btn:hover {
  background-color: #f5c6cb;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
}

.pagination {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.pagination-btn {
  padding: var(--spacing-sm) var(--spacing-md);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.pagination-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.pagination-info {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-black);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .order-footer {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .order-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .order-item {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-xs);
  }

  .login-actions {
    flex-direction: column;
  }

  .pagination {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .page-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .orders-content {
    margin: 0 var(--spacing-md);
  }

  .action-btn {
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
  }
}
</style>
