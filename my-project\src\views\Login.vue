<template>
  <div class="login-page">
    <div class="container">
      <div class="login-container">
        <div class="login-window">
          <div class="window-header">
            <div class="window-controls">
              <span class="control-dot"></span>
              <span class="control-dot"></span>
              <span class="control-dot"></span>
            </div>
            <h2 class="window-title">用户登录</h2>
          </div>

          <div class="login-content">
            <h1 class="login-title">欢迎回来</h1>
            <p class="login-subtitle">登录您的EDU邮箱账户</p>

            <form @submit.prevent="handleLogin" class="login-form">
              <div class="form-group">
                <label for="email" class="form-label">邮箱地址</label>
                <input
                  id="email"
                  v-model="loginForm.email"
                  type="email"
                  class="input-field"
                  placeholder="请输入您的邮箱地址"
                  required
                />
              </div>

              <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <input
                  id="password"
                  v-model="loginForm.password"
                  type="password"
                  class="input-field"
                  placeholder="请输入您的密码"
                  required
                />
              </div>

              <div class="form-actions">
                <button type="submit" class="btn btn-primary login-btn">
                  登录
                </button>
              </div>

              <div class="form-footer">
                <p class="register-link">
                  还没有账户？
                  <router-link to="/register" class="link">立即注册</router-link>
                </p>
              </div>
            </form>
          </div>
        </div>

        <!-- 装饰性图形 -->
        <div class="decorative-graphics">
          <div class="grid-pattern"></div>
          <div class="geometric-shape shape-1"></div>
          <div class="geometric-shape shape-2"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        email: '',
        password: ''
      }
    }
  },
  methods: {
    handleLogin() {
      // TODO: 实现登录逻辑，暂时只是控制台输出
      console.log('登录表单提交:', this.loginForm)
      alert('登录功能将在后端接口对接后实现')
    }
  }
}
</script>

<style scoped>
.login-page {
  min-height: calc(100vh - 80px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg) 0;
}

.login-container {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.login-window {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
}

.window-header {
  background-color: var(--secondary-surface);
  border-bottom: var(--border-width) solid var(--neutral-black);
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.window-controls {
  display: flex;
  gap: 6px;
}

.control-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--neutral-black);
  border: 1px solid var(--neutral-black);
}

.window-title {
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  margin: 0;
  text-transform: uppercase;
}

.login-content {
  padding: var(--spacing-xl) var(--spacing-lg);
}

.login-title {
  font-size: 2rem;
  margin-bottom: var(--spacing-xs);
  text-align: center;
}

.login-subtitle {
  text-align: center;
  color: #666;
  margin-bottom: var(--spacing-xl);
}

.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  font-size: 0.9rem;
}

.form-actions {
  margin: var(--spacing-xl) 0 var(--spacing-lg) 0;
}

.login-btn {
  width: 100%;
  padding: var(--spacing-md);
  font-size: 1.1rem;
}

.form-footer {
  text-align: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid #eee;
}

.register-link {
  margin: 0;
  font-size: 0.9rem;
}

.link {
  color: var(--neutral-black);
  text-decoration: underline;
  font-weight: var(--font-weight-medium);
}

.link:hover {
  color: var(--accent-highlight);
}

/* 装饰性图形 */
.decorative-graphics {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.grid-pattern {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background-image:
    linear-gradient(var(--neutral-black) 1px, transparent 1px),
    linear-gradient(90deg, var(--neutral-black) 1px, transparent 1px);
  background-size: 10px 10px;
  opacity: 0.3;
}

.geometric-shape {
  position: absolute;
  border: var(--border-width) solid var(--neutral-black);
}

.shape-1 {
  width: 60px;
  height: 60px;
  background-color: var(--accent-highlight);
  border-radius: 50%;
  top: 20%;
  left: -30px;
  opacity: 0.8;
}

.shape-2 {
  width: 40px;
  height: 40px;
  background-color: var(--secondary-surface);
  bottom: 20%;
  right: -20px;
  transform: rotate(45deg);
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    margin: 0 var(--spacing-md);
  }

  .login-content {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .login-title {
    font-size: 1.5rem;
  }
}
</style>
