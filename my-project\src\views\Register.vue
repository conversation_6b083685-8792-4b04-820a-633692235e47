<template>
  <div class="register-page">
    <div class="container">
      <div class="register-container">
        <div class="register-window">
          <div class="window-header">
            <div class="window-controls">
              <span class="control-dot"></span>
              <span class="control-dot"></span>
              <span class="control-dot"></span>
            </div>
            <h2 class="window-title">用户注册</h2>
          </div>

          <div class="register-content">
            <h1 class="register-title">创建账户</h1>
            <p class="register-subtitle">注册EDU邮箱平台账户</p>

            <form @submit.prevent="handleRegister" class="register-form">
              <div class="form-row">
                <div class="form-group">
                  <label for="username" class="form-label">用户名</label>
                  <input
                    id="username"
                    v-model="registerForm.username"
                    type="text"
                    class="input-field"
                    placeholder="请输入用户名"
                    required
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="email" class="form-label">邮箱地址</label>
                  <input
                    id="email"
                    v-model="registerForm.email"
                    type="email"
                    class="input-field"
                    placeholder="请输入您的邮箱地址"
                    required
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="password" class="form-label">密码</label>
                  <input
                    id="password"
                    v-model="registerForm.password"
                    type="password"
                    class="input-field"
                    placeholder="请输入密码（至少6位）"
                    required
                    minlength="6"
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="confirmPassword" class="form-label">确认密码</label>
                  <input
                    id="confirmPassword"
                    v-model="registerForm.confirmPassword"
                    type="password"
                    class="input-field"
                    placeholder="请再次输入密码"
                    required
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="phone" class="form-label">手机号码（可选）</label>
                  <input
                    id="phone"
                    v-model="registerForm.phone"
                    type="tel"
                    class="input-field"
                    placeholder="请输入手机号码"
                  />
                </div>
              </div>

              <div class="form-group checkbox-group">
                <label class="checkbox-label">
                  <input
                    v-model="registerForm.agreeTerms"
                    type="checkbox"
                    class="checkbox-input"
                    required
                  />
                  <span class="checkbox-custom"></span>
                  我已阅读并同意
                  <a href="#" class="link">服务条款</a>
                  和
                  <a href="#" class="link">隐私政策</a>
                </label>
              </div>

              <div class="form-actions">
                <button type="submit" class="btn btn-primary register-btn" :disabled="!isFormValid">
                  创建账户
                </button>
              </div>

              <div class="form-footer">
                <p class="login-link">
                  已有账户？
                  <router-link to="/login" class="link">立即登录</router-link>
                </p>
              </div>
            </form>
          </div>
        </div>

        <!-- 装饰性图形 -->
        <div class="decorative-graphics">
          <div class="grid-pattern"></div>
          <div class="geometric-shape shape-1"></div>
          <div class="geometric-shape shape-2"></div>
          <div class="geometric-shape shape-3"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Register',
  data() {
    return {
      registerForm: {
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        phone: '',
        agreeTerms: false
      }
    }
  },
  computed: {
    isFormValid() {
      return (
        this.registerForm.username &&
        this.registerForm.email &&
        this.registerForm.password &&
        this.registerForm.confirmPassword &&
        this.registerForm.password === this.registerForm.confirmPassword &&
        this.registerForm.agreeTerms
      )
    }
  },
  methods: {
    handleRegister() {
      if (!this.isFormValid) {
        alert('请填写完整信息并确保密码一致')
        return
      }

      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        alert('两次输入的密码不一致')
        return
      }

      // TODO: 实现注册逻辑，暂时只是控制台输出
      console.log('注册表单提交:', this.registerForm)
      alert('注册功能将在后端接口对接后实现')
    }
  }
}
</script>

<style scoped>
.register-page {
  min-height: calc(100vh - 80px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg) 0;
}

.register-container {
  position: relative;
  width: 100%;
  max-width: 600px;
}

.register-window {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
}

.window-header {
  background-color: var(--secondary-surface);
  border-bottom: var(--border-width) solid var(--neutral-black);
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.window-controls {
  display: flex;
  gap: 6px;
}

.control-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--neutral-black);
  border: 1px solid var(--neutral-black);
}

.window-title {
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  margin: 0;
  text-transform: uppercase;
}

.register-content {
  padding: var(--spacing-xl) var(--spacing-lg);
}

.register-title {
  font-size: 2rem;
  margin-bottom: var(--spacing-xs);
  text-align: center;
}

.register-subtitle {
  text-align: center;
  color: #666;
  margin-bottom: var(--spacing-xl);
}

.register-form {
  width: 100%;
}

.form-row {
  margin-bottom: var(--spacing-lg);
}

.form-group {
  width: 100%;
}

.form-label {
  display: block;
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  font-size: 0.9rem;
}

.checkbox-group {
  margin: var(--spacing-lg) 0;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
  line-height: 1.4;
  cursor: pointer;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: var(--border-width) solid var(--neutral-black);
  border-radius: 4px;
  background-color: var(--neutral-white);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 2px;
  transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-custom {
  background-color: var(--secondary-surface);
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  color: var(--neutral-black);
  font-weight: bold;
  font-size: 14px;
}

.form-actions {
  margin: var(--spacing-xl) 0 var(--spacing-lg) 0;
}

.register-btn {
  width: 100%;
  padding: var(--spacing-md);
  font-size: 1.1rem;
}

.register-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.register-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.form-footer {
  text-align: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid #eee;
}

.login-link {
  margin: 0;
  font-size: 0.9rem;
}

.link {
  color: var(--neutral-black);
  text-decoration: underline;
  font-weight: var(--font-weight-medium);
}

.link:hover {
  color: var(--accent-highlight);
}

/* 装饰性图形 */
.decorative-graphics {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.grid-pattern {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 120px;
  height: 120px;
  background-image:
    linear-gradient(var(--neutral-black) 1px, transparent 1px),
    linear-gradient(90deg, var(--neutral-black) 1px, transparent 1px);
  background-size: 12px 12px;
  opacity: 0.3;
}

.geometric-shape {
  position: absolute;
  border: var(--border-width) solid var(--neutral-black);
}

.shape-1 {
  width: 50px;
  height: 50px;
  background-color: var(--accent-highlight);
  border-radius: 50%;
  top: 15%;
  left: -25px;
  opacity: 0.8;
}

.shape-2 {
  width: 35px;
  height: 35px;
  background-color: var(--secondary-surface);
  bottom: 30%;
  right: -18px;
  transform: rotate(45deg);
  opacity: 0.8;
}

.shape-3 {
  width: 60px;
  height: 30px;
  background-color: var(--primary-background);
  top: 50%;
  left: -30px;
  opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .register-container {
    margin: 0 var(--spacing-md);
  }

  .register-content {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .register-title {
    font-size: 1.5rem;
  }
}
</style>
