import api from './api'

// 商品服务
export const productService = {
  // 获取商品列表
  async getProducts(params = {}) {
    try {
      const queryParams = new URLSearchParams()
      
      // 添加查询参数
      if (params.page) queryParams.append('page', params.page)
      if (params.limit) queryParams.append('limit', params.limit)
      if (params.status) queryParams.append('status', params.status)
      if (params.search) queryParams.append('search', params.search)
      if (params.sort) queryParams.append('sort', params.sort)
      
      const queryString = queryParams.toString()
      const url = queryString ? `/products?${queryString}` : '/products'
      
      const response = await api.get(url)
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || '获取商品列表失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 获取商品详情
  async getProductById(id) {
    try {
      if (!id) {
        throw new Error('商品ID不能为空')
      }
      
      const response = await api.get(`/products/${id}`)
      
      if (response.success) {
        return response.data.product
      } else {
        throw new Error(response.message || '获取商品详情失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 获取热门商品
  async getHotProducts(limit = 6) {
    try {
      const response = await api.get(`/products/hot?limit=${limit}`)
      
      if (response.success) {
        return response.data.products
      } else {
        throw new Error(response.message || '获取热门商品失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 搜索商品
  async searchProducts(keyword, params = {}) {
    try {
      const searchParams = {
        search: keyword,
        ...params
      }
      
      return await this.getProducts(searchParams)
    } catch (error) {
      throw error
    }
  }
}

export default productService
