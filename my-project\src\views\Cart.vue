<template>
  <div class="cart-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">购物车</h1>
        <p class="page-subtitle">管理您的商品</p>
      </div>
      
      <!-- 未登录提示 -->
      <div v-if="!isAuthenticated" class="login-prompt-section">
        <div class="login-prompt-card">
          <h3>请先登录</h3>
          <p>登录后即可查看和管理您的购物车</p>
          <div class="login-actions">
            <router-link to="/login" class="btn btn-primary">登录</router-link>
            <router-link to="/register" class="btn btn-accent">注册</router-link>
          </div>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-else-if="isLoading" class="loading-section">
        <div class="loading-card">
          <p>正在加载购物车...</p>
        </div>
      </div>
      
      <!-- 购物车内容 -->
      <div v-else-if="hasItems" class="cart-content">
        <!-- 购物车列表 -->
        <div class="cart-items-section">
          <div class="cart-items-header">
            <h2>商品列表</h2>
            <button @click="handleClearCart" class="btn clear-btn">
              清空购物车
            </button>
          </div>
          
          <div class="cart-items-list">
            <div
              v-for="item in cartItems"
              :key="item.id"
              class="cart-item"
              :class="{ 'out-of-stock': !item.in_stock }"
            >
              <div class="item-image">
                <img :src="item.school_logo_url" :alt="item.product_name" />
              </div>
              
              <div class="item-info">
                <h3 class="item-name">{{ item.product_name }}</h3>
                <p class="item-price">单价: ¥{{ item.unit_price }}</p>
                <div v-if="!item.in_stock" class="stock-warning">
                  商品已下架或缺货
                </div>
              </div>
              
              <div class="item-quantity">
                <label class="quantity-label">数量:</label>
                <div class="quantity-controls">
                  <button
                    @click="decreaseQuantity(item)"
                    :disabled="item.quantity <= 1 || updatingItems.has(item.id)"
                    class="btn quantity-btn"
                  >
                    -
                  </button>
                  <span class="quantity-display">{{ item.quantity }}</span>
                  <button
                    @click="increaseQuantity(item)"
                    :disabled="item.quantity >= 10 || !item.in_stock || updatingItems.has(item.id)"
                    class="btn quantity-btn"
                  >
                    +
                  </button>
                </div>
              </div>
              
              <div class="item-total">
                <span class="total-price">¥{{ item.total_price }}</span>
              </div>
              
              <div class="item-actions">
                <button
                  @click="removeItem(item)"
                  :disabled="updatingItems.has(item.id)"
                  class="btn remove-btn"
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 购物车汇总 -->
        <div class="cart-summary-section">
          <div class="cart-summary">
            <h2>订单汇总</h2>
            <div class="summary-details">
              <div class="summary-item">
                <span class="label">商品种类:</span>
                <span class="value">{{ cartSummary.total_items }}种</span>
              </div>
              <div class="summary-item">
                <span class="label">商品数量:</span>
                <span class="value">{{ cartSummary.total_quantity }}件</span>
              </div>
              <div class="summary-item">
                <span class="label">有效商品:</span>
                <span class="value">{{ cartSummary.valid_items }}种</span>
              </div>
              <div class="summary-item total">
                <span class="label">总金额:</span>
                <span class="value">¥{{ cartSummary.total_amount }}</span>
              </div>
            </div>
            
            <div class="checkout-actions">
              <button
                @click="handleCheckout"
                :disabled="cartSummary.valid_items === 0"
                class="btn btn-primary checkout-btn"
              >
                去结算 ({{ cartSummary.valid_items }}件商品)
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空购物车 -->
      <div v-else class="empty-cart-section">
        <div class="empty-cart-card">
          <h3>购物车是空的</h3>
          <p>快去挑选您喜欢的EDU邮箱吧！</p>
          <router-link to="/products" class="btn btn-primary">
            去购物
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { useCart } from '../composables/useCart'
import { useToast } from '../composables/useToast'

export default {
  name: 'Cart',
  setup() {
    const router = useRouter()
    const { isAuthenticated } = useAuth()
    const {
      cartItems,
      cartSummary,
      hasItems,
      isLoading,
      loadCart,
      updateCartItem,
      removeCartItem,
      clearCart
    } = useCart()
    const { success, error } = useToast()
    
    // 正在更新的商品项
    const updatingItems = ref(new Set())
    
    // 增加数量
    const increaseQuantity = async (item) => {
      if (item.quantity >= 10 || !item.in_stock) return
      
      updatingItems.value.add(item.id)
      try {
        await updateCartItem(item.id, item.quantity + 1)
        success('数量已更新')
      } catch (err) {
        error(err.message || '更新失败')
      } finally {
        updatingItems.value.delete(item.id)
      }
    }
    
    // 减少数量
    const decreaseQuantity = async (item) => {
      if (item.quantity <= 1) return
      
      updatingItems.value.add(item.id)
      try {
        await updateCartItem(item.id, item.quantity - 1)
        success('数量已更新')
      } catch (err) {
        error(err.message || '更新失败')
      } finally {
        updatingItems.value.delete(item.id)
      }
    }
    
    // 删除商品
    const removeItem = async (item) => {
      updatingItems.value.add(item.id)
      try {
        await removeCartItem(item.id)
        success('商品已删除')
      } catch (err) {
        error(err.message || '删除失败')
      } finally {
        updatingItems.value.delete(item.id)
      }
    }
    
    // 清空购物车
    const handleClearCart = async () => {
      if (!confirm('确定要清空购物车吗？')) return
      
      try {
        await clearCart()
        success('购物车已清空')
      } catch (err) {
        error(err.message || '清空失败')
      }
    }
    
    // 去结算
    const handleCheckout = () => {
      // TODO: 实现结算功能
      success('结算功能将在后续版本中实现')
    }
    
    // 组件挂载时加载购物车
    onMounted(() => {
      if (isAuthenticated.value) {
        loadCart()
      }
    })
    
    return {
      isAuthenticated,
      cartItems,
      cartSummary,
      hasItems,
      isLoading,
      updatingItems,
      increaseQuantity,
      decreaseQuantity,
      removeItem,
      handleClearCart,
      handleCheckout
    }
  }
}
</script>

<style scoped>
.cart-page {
  min-height: calc(100vh - 80px);
  padding: var(--spacing-lg) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
}

.login-prompt-section,
.loading-section,
.empty-cart-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.login-prompt-card,
.loading-card,
.empty-cart-card {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  max-width: 400px;
}

.login-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-lg);
}

.cart-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
}

.cart-items-section {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
}

.cart-items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--secondary-surface);
  border-bottom: var(--border-width) solid var(--neutral-black);
}

.cart-items-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.clear-btn {
  background-color: #ffebee;
  color: #c62828;
  border: var(--border-width) solid #c62828;
}

.clear-btn:hover {
  background-color: #ffcdd2;
}

.cart-items-list {
  padding: var(--spacing-lg);
}

.cart-item {
  display: grid;
  grid-template-columns: 80px 1fr auto auto auto;
  gap: var(--spacing-md);
  align-items: center;
  padding: var(--spacing-md);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  background-color: var(--neutral-white);
}

.cart-item:last-child {
  margin-bottom: 0;
}

.cart-item.out-of-stock {
  opacity: 0.6;
  background-color: #f5f5f5;
}

.item-image {
  width: 80px;
  height: 80px;
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  overflow: hidden;
  background-color: var(--secondary-surface);
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.item-name {
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
  margin: 0;
}

.item-price {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

.stock-warning {
  font-size: 0.8rem;
  color: #c62828;
  font-weight: var(--font-weight-medium);
}

.item-quantity {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.quantity-label {
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.quantity-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: bold;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.quantity-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.quantity-display {
  min-width: 40px;
  text-align: center;
  font-weight: var(--font-weight-medium);
  font-size: 1.1rem;
}

.item-total {
  text-align: center;
}

.total-price {
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
  color: var(--accent-highlight);
}

.remove-btn {
  background-color: #ffebee;
  color: #c62828;
  border: var(--border-width) solid #c62828;
  padding: var(--spacing-xs) var(--spacing-sm);
}

.remove-btn:hover {
  background-color: #ffcdd2;
}

.remove-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 #c62828;
}

.remove-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 #c62828;
  background-color: #ffebee;
}

.cart-summary-section {
  align-self: start;
}

.cart-summary {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  padding: var(--spacing-lg);
  position: sticky;
  top: var(--spacing-lg);
}

.cart-summary h2 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 1.5rem;
  text-align: center;
}

.summary-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius);
}

.summary-item.total {
  background-color: var(--secondary-surface);
  border: var(--border-width) solid var(--neutral-black);
  font-weight: var(--font-weight-bold);
  font-size: 1.1rem;
}

.summary-item .label {
  color: #666;
}

.summary-item .value {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-black);
}

.summary-item.total .value {
  color: var(--accent-highlight);
  font-size: 1.2rem;
}

.checkout-btn {
  width: 100%;
  padding: var(--spacing-md);
  font-size: 1.1rem;
}

.checkout-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.checkout-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .cart-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .cart-summary {
    position: static;
  }
}

@media (max-width: 768px) {
  .cart-item {
    grid-template-columns: 60px 1fr;
    gap: var(--spacing-sm);
  }

  .item-quantity,
  .item-total,
  .item-actions {
    grid-column: 1 / -1;
    justify-self: stretch;
  }

  .item-quantity {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .item-total {
    text-align: left;
  }

  .login-actions {
    flex-direction: column;
  }

  .page-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .cart-items-header {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .cart-items-header h2 {
    font-size: 1.2rem;
  }

  .item-image {
    width: 60px;
    height: 60px;
  }
}
</style>
