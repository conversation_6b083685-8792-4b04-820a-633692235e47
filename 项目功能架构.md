将完全基于`Vue3 (前端) + Node.js (后端) + MySQL (数据库)`技术栈，确保方案的可落地性。


### **项目核心概述**

* **项目名称:** EDU邮箱自动售卖平台
* **核心业务:** 在线销售不同高校的EDU邮箱账号，实现用户自助下单、自动发货、订单管理的全流程自动化。
* **目标用户:** 需要EDU邮箱以获取教育优惠、学术资源等服务的各类人群。
* **商业模式:** 通过赚取EDU邮箱的差价来盈利，核心竞争力在于货源的稳定性和发货的即时性。

### **用户角色与权限**

系统主要包含以下三种角色：

1.  **游客 (Guest):** 未登录用户。
    * 权限：可以浏览网站首页、商品列表、商品详情、帮助中心等公开信息页面。无法进行购买操作。
2.  **注册用户 (Customer):** 网站的消费者。
    * 权限：拥有游客所有权限，此外还可以进行登录注册、下单购买、查看自己的订单、管理个人信息等。
3.  **管理员 (Admin):** 网站的运营者和所有者。（暂不实现后台管理）
    * 权限：拥有后台系统的最高权限，负责管理商品（邮箱库存）、订单、用户、网站内容和系统设置等。

---

### **功能模块设计**

我将网站功能划分为 **前台功能 (面向用户)**

#### **一、 前台功能 (Vue3)**

**1. 首页 (Homepage)**
* **功能描述:** 网站的门面，旨在吸引用户并引导消费。
* **具体实现:**
    * **顶部导航栏:** Logo、商品分类、帮助中心、登录/注册入口。
    * **热门商品推荐:** 展示销量最高或主推的几款EDU邮箱产品。
    * **网站优势说明:** 通过图标和简短文字介绍网站的特点（安全、快速、稳定）。
    * **页脚:** 关于我们、服务条款、联系方式等链接。

**2. 商品模块 (Product Module)**
* **功能描述:** EDU邮箱的展示和筛选，方便用户快速找到所需商品。
* **具体实现:**
    * **商品列表页:**
        * 以卡片形式展示所有在售的EDU邮箱。
        * 每个卡片包含：学校名称、Logo、价格、简要描述、库存状态（显示“有货”或“补货中”）。
        * 支持按价格、功能等维度进行筛选和排序。
    * **商品详情页:**
        * 点击列表页商品后进入。
        * 详细展示：商品标题、高清图（学校Logo）、售价、库存状态（显示“有货”或“补货中”）。
        * 详细描述：包含邮箱的后缀、具体权益、使用注意事项、质保期限等关键信息。
        * 明确的“立即购买”和“加入购物车”按钮。

**3. 购物车 (Shopping Cart)**
* **功能描述:** 允许用户临时存放多个意向商品，并统一结算。
* **具体实现:**
    * 用户可以将商品加入购物车。
    * 在购物车页面，可以修改商品数量（通常为1）、删除商品。
    * 实时计算并显示订单总金额。
    * 提供“去结算”按钮，引导用户进入下单流程。

**4. 下单与支付 (Checkout & Payment)**
* **功能描述:** 用户完成购买的关键流程，核心在于简洁和安全。
* **具体实现:**
    * **信息确认:** 用户确认购买的商品和金额。
    * **接收邮箱:** 要求用户填写一个用于接收商品账号密码的常用邮箱（非常重要）。
    * **支付方式选择:** 支持主流支付方式，如支付宝、微信支付。Node.js可以方便地集成这些支付SDK。(先放着)
    * **支付跳转:** 点击支付后，生成订单，并跳转至相应的支付网关页面。
    * **支付结果回调:** 支付成功后，页面自动跳转到“支付成功”提示页，并触发后台自动发货逻辑。

**5. 用户中心 (User Center)**
* **功能描述:** 已登录用户的个人空间，用于管理订单和个人信息。
* **具体实现:**
    * **我的订单:**
        * 列表形式展示所有历史订单，包括订单号、商品名称、金额、下单时间、订单状态（待支付、已完成、已取消）。
        * **核心功能:** 对于“已完成”的订单，用户点击“查看详情”后，可以直接看到系统自动发货的EDU邮箱账号和密码。这是核心交付环节。
    * **个人资料:** 用户可以修改自己的登录密码、联系邮箱等。


#### **二、 后台管理系统 (Vue3 + Node.js API) **后台管理系统 (面向管理员)** 两大部分。

**1. 登录与仪表盘 (Login & Dashboard)**
* **功能描述:** 管理员入口和核心数据概览。
* **具体实现:**
    * **安全登录:** 独立的后台登录页面，有防暴力破解机制。
    * **数据统计:** 仪表盘展示核心KPI，如：今日订单数、今日销售额、待处理工单、总用户数、商品库存预警等。

**2. 商品管理 (Product Management)**
* **功能描述:** 管理网站上销售的EDU邮箱“商品种类”和“实际库存”。
* **具体实现:**
    * **商品分类管理:** 添加或编辑商品“种类”，可以设置种类名称、价格、描述、质保期等。
    * **库存管理 (核心):**
        * 管理员可以为每个商品分类 **批量导入** 邮箱账号密码库存（例如通过上传Excel或CSV文件）。
        * 数据库中每个账号密码都应有一个状态字段：`可用 (Available)`、`已售出 (Sold)`、`锁定 (Locked)`、`异常 (Defective)`。
        * 列表展示所有库存的详细信息和状态。
        * 支持手动添加、修改或删除单个库存。

**3. 订单管理 (Order Management)**
* **功能描述:** 查看和管理所有用户的交易订单。
* **具体实现:**
    * **订单列表:** 展示所有订单的详细信息（订单号、用户、商品、金额、支付状态、发货状态、时间等）。
    * **订单搜索:** 支持按订单号、用户邮箱等条件进行搜索。
    * **订单详情:** 查看单个订单的完整信息，包括支付流水号以及 **系统自动分配给该订单的具体邮箱账号密码**。
    * **手动处理:** 在极端情况下（如自动发货失败），管理员可以手动为订单标记完成或进行退款操作。

**4. 用户管理 (User Management)**
* **功能描述:** 管理所有注册用户。
* **具体实现:**
    * **用户列表:** 展示所有注册用户的信息（ID、用户名、注册邮箱、注册时间）。
    * **用户搜索:** 支持按用户名或邮箱搜索用户。
    * **账户操作:** 可以查看某个用户的所有订单，或在必要时禁用某个用户账户。

**5. 系统设置 (System Settings)**
* **功能描述:** 配置网站的基础信息和关键参数。
* **具体实现:**
    * **网站信息设置:** 修改网站名称、Logo、SEO关键词等。
    * **支付接口配置:** 修改支付接口密钥、回调地址等信息。
    * **邮件通知配置:** 设置发件邮箱服务器（SMTP），用于发送订单确认、发货通知等邮件。