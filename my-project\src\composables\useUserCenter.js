import { ref, computed } from 'vue'
import { userService } from '../services/user'
import { useAuth } from './useAuth'

// 全局用户中心状态
const userProfile = ref(null)
const userStats = ref({
  total_orders: 0,
  completed_orders: 0,
  pending_orders: 0,
  total_spent: 0,
  total_accounts: 0
})
const isLoading = ref(false)

export function useUserCenter() {
  const { isAuthenticated, user } = useAuth()
  
  // 计算属性
  const hasProfile = computed(() => !!userProfile.value)
  const completionRate = computed(() => {
    if (userStats.value.total_orders === 0) return 0
    return Math.round((userStats.value.completed_orders / userStats.value.total_orders) * 100)
  })
  
  // 加载用户资料
  const loadUserProfile = async () => {
    if (!isAuthenticated.value) {
      userProfile.value = null
      return
    }
    
    isLoading.value = true
    try {
      const profile = await userService.getUserProfile()
      userProfile.value = profile
    } catch (error) {
      console.error('加载用户资料失败:', error)
      userProfile.value = null
    } finally {
      isLoading.value = false
    }
  }
  
  // 加载用户统计信息
  const loadUserStats = async () => {
    if (!isAuthenticated.value) {
      userStats.value = {
        total_orders: 0,
        completed_orders: 0,
        pending_orders: 0,
        total_spent: 0,
        total_accounts: 0
      }
      return
    }
    
    try {
      const stats = await userService.getUserStats()
      userStats.value = stats
    } catch (error) {
      console.error('加载用户统计失败:', error)
      userStats.value = {
        total_orders: 0,
        completed_orders: 0,
        pending_orders: 0,
        total_spent: 0,
        total_accounts: 0
      }
    }
  }
  
  // 更新用户资料
  const updateUserProfile = async (profileData) => {
    if (!isAuthenticated.value) {
      throw new Error('请先登录')
    }

    try {
      const updatedProfile = await userService.updateProfile(profileData)
      userProfile.value = updatedProfile

      return updatedProfile
    } catch (error) {
      throw error
    }
  }
  
  // 修改密码
  const changePassword = async (passwordData) => {
    if (!isAuthenticated.value) {
      throw new Error('请先登录')
    }
    
    try {
      await userService.changePassword(passwordData)
      return true
    } catch (error) {
      throw error
    }
  }
  
  // 获取用户订单历史
  const getUserOrders = async (page = 1, limit = 10, status = null) => {
    if (!isAuthenticated.value) {
      throw new Error('请先登录')
    }
    
    try {
      const ordersData = await userService.getUserOrders(page, limit, status)
      return ordersData
    } catch (error) {
      throw error
    }
  }
  
  // 刷新所有数据
  const refreshUserData = async () => {
    if (!isAuthenticated.value) return
    
    await Promise.all([
      loadUserProfile(),
      loadUserStats()
    ])
  }
  
  // 清除用户数据
  const clearUserData = () => {
    userProfile.value = null
    userStats.value = {
      total_orders: 0,
      completed_orders: 0,
      pending_orders: 0,
      total_spent: 0,
      total_accounts: 0
    }
  }
  
  return {
    // 状态
    userProfile: computed(() => userProfile.value),
    userStats: computed(() => userStats.value),
    hasProfile,
    completionRate,
    isLoading: computed(() => isLoading.value),
    
    // 方法
    loadUserProfile,
    loadUserStats,
    updateUserProfile,
    changePassword,
    getUserOrders,
    refreshUserData,
    clearUserData
  }
}
