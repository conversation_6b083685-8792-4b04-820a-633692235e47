import { ref, computed } from 'vue'
import { cartService } from '../services/cart'
import { useAuth } from './useAuth'

// 全局购物车状态
const cartItems = ref([])
const cartSummary = ref({
  total_items: 0,
  total_quantity: 0,
  total_amount: 0,
  valid_items: 0
})
const isLoading = ref(false)

export function useCart() {
  const { isAuthenticated } = useAuth()
  
  // 计算属性
  const cartCount = computed(() => cartSummary.value.total_quantity || 0)
  const cartTotal = computed(() => cartSummary.value.total_amount || 0)
  const hasItems = computed(() => cartItems.value.length > 0)
  
  // 加载购物车数据
  const loadCart = async () => {
    if (!isAuthenticated.value) {
      cartItems.value = []
      cartSummary.value = {
        total_items: 0,
        total_quantity: 0,
        total_amount: 0,
        valid_items: 0
      }
      return
    }
    
    isLoading.value = true
    try {
      const data = await cartService.getCartItems()
      cartItems.value = data.cart_items || []
      cartSummary.value = data.summary || {
        total_items: 0,
        total_quantity: 0,
        total_amount: 0,
        valid_items: 0
      }
    } catch (error) {
      console.error('加载购物车失败:', error)
      cartItems.value = []
      cartSummary.value = {
        total_items: 0,
        total_quantity: 0,
        total_amount: 0,
        valid_items: 0
      }
    } finally {
      isLoading.value = false
    }
  }
  
  // 添加商品到购物车
  const addToCart = async (productId, quantity = 1) => {
    if (!isAuthenticated.value) {
      throw new Error('请先登录')
    }

    try {
      await cartService.addToCart(productId, quantity)
      // 重新加载购物车数据
      await loadCart()
      return true
    } catch (error) {
      throw error
    }
  }
  
  // 更新购物车商品数量
  const updateCartItem = async (cartItemId, quantity) => {
    if (!isAuthenticated.value) {
      throw new Error('请先登录')
    }
    
    try {
      await cartService.updateCartItem(cartItemId, quantity)
      // 重新加载购物车数据
      await loadCart()
      return true
    } catch (error) {
      throw error
    }
  }
  
  // 删除购物车商品
  const removeCartItem = async (cartItemId) => {
    if (!isAuthenticated.value) {
      throw new Error('请先登录')
    }
    
    try {
      await cartService.removeCartItem(cartItemId)
      // 重新加载购物车数据
      await loadCart()
      return true
    } catch (error) {
      throw error
    }
  }
  
  // 清空购物车
  const clearCart = async () => {
    if (!isAuthenticated.value) {
      throw new Error('请先登录')
    }
    
    try {
      await cartService.clearCart()
      // 清空本地状态
      cartItems.value = []
      cartSummary.value = {
        total_items: 0,
        total_quantity: 0,
        total_amount: 0,
        valid_items: 0
      }
      return true
    } catch (error) {
      throw error
    }
  }
  
  // 获取指定商品在购物车中的数量
  const getCartItemQuantity = (productId) => {
    const item = cartItems.value.find(item => item.product_id === productId)
    return item ? item.quantity : 0
  }
  
  // 检查商品是否在购物车中
  const isInCart = (productId) => {
    return cartItems.value.some(item => item.product_id === productId)
  }
  
  return {
    // 状态
    cartItems: computed(() => cartItems.value),
    cartSummary: computed(() => cartSummary.value),
    cartCount,
    cartTotal,
    hasItems,
    isLoading: computed(() => isLoading.value),
    
    // 方法
    loadCart,
    addToCart,
    updateCartItem,
    removeCartItem,
    clearCart,
    getCartItemQuantity,
    isInCart
  }
}
