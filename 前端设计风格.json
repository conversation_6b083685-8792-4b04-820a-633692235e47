{"designSystemProfile": {"description": "一个融合了粗犷主义（Brutalism）、复古科技感和现代简约风格的设计系统。其特点是高对比度的色彩、粗黑的轮廓线、几何形状和清晰的块状布局，营造出一种既复古又前卫的视觉体验。", "name": "Neo-Brutalist Tech", "version": "1.0", "designPrinciples": {"overallVibe": "大胆、复古科技、结构化、高对比度", "keyFeatures": ["Thick Outlines: 所有交互元素（按钮、卡片、输入框）都使用统一粗细的黑色实线轮廓。", "Hard Shadows / 2.5D Effect: 使用硬朗、不模糊的黑色阴影或偏移的轮廓来创造一种伪3D或“挤出”效果，增强元素的立体感。", "High-Contrast Palette: 采用鲜艳、饱和度高的背景色和强调色，与中性的黑白色形成强烈对比。", "Geometric Simplicity: 设计语言以基础几何图形（矩形、圆形、直线）为主，圆角弧度统一。", "Structured Layout: 采用清晰的网格布局，元素间距明确，视觉层次分明。", "Minimalism in Detail: 避免使用渐变、复杂的纹理或逼真的阴影，专注于平面色彩和线条。"]}, "colorPalette": {"primary": {"background": "#F9F900", "description": "充满活力的亮黄色，通常用作整个页面的背景。"}, "secondary": {"surface": "#43C4B2", "description": "中等饱和度的青绿色，用作主要内容区域的背景或大型UI元素的填充色。"}, "accent": {"highlight": "#FF00FF", "description": "非常鲜艳的品红色/粉色，用于吸引眼球的元素，如模态窗口或关键图形。"}, "neutral": {"baseWhite": "#FFFFFF", "baseBlack": "#000000", "description": "纯白和纯黑。白色主要用于填充，黑色用于文字、轮廓和硬阴影。"}}, "typography": {"family": "Monospace, Sans-Serif", "description": "推荐使用具有等宽或几何感的无衬线字体（例如 'Roboto Mono', 'Source Code Pro', or a similar geometric sans-serif）。", "styles": {"heading1": {"fontWeight": "700", "case": "Sentence case", "color": "neutral.baseBlack", "description": "用于页面的主标题，字号最大，字重最粗。"}, "button": {"fontWeight": "500", "case": "UPPERCASE", "color": "neutral.baseBlack", "description": "用于按钮文本，全大写以示强调。"}, "body": {"fontWeight": "400", "case": "Sentence case", "color": "neutral.baseBlack", "description": "用于常规内容或标签文本。"}}}, "layout": {"container": {"style": "Windowed", "description": "内容被放置在一个类似应用程序或浏览器窗口的框架内，窗口外是主背景色。"}, "spacing": {"principle": "Grid-based", "density": "Open", "description": "使用基于网格的系统来对齐元素。元素之间保持宽敞的间距，创造开放、不拥挤的感觉。"}, "structure": ["Header: 顶部栏，包含左侧的Logo/标识区域和右侧的全局控件（如搜索框）。", "Hero Section: 标题和主要行动号召（CTA）所在的突出区域，通常伴有装饰性的抽象图形。", "Content Grid: 用于展示条目（如卡片）的网格布局，通常为2-4列。"]}, "components": {"button": {"shape": "Pill / Rounded Rectangle", "style": "Outlined", "fill": "neutral.baseWhite", "stroke": "2px solid neutral.baseBlack", "shadow": "Hard, offset, black, 2-3px", "states": {"hover": "Subtle position shift or color change", "active": "Shadow collapses or inverts"}}, "card": {"shape": "Rounded Rectangle", "style": "Outlined, Composite", "fill": "secondary.surface", "stroke": "2px solid neutral.baseBlack", "shadow": "Hard, offset, black, 2-3px", "composition": "通常由一个主内容区和一个白色元数据区（底部）或标签（顶部）组成。"}, "inputField": {"shape": "Rounded Rectangle", "style": "Outlined", "fill": "neutral.baseWhite", "stroke": "2px solid neutral.baseBlack", "placeholder": "Simple, sans-serif text in a muted color."}, "window": {"shape": "Rounded Rectangle", "style": "Outlined", "header": "Distinct header bar, sometimes with stylized window controls (e.g., three dots).", "stroke": "2px solid neutral.baseBlack", "fill": "Can use accent or secondary colors."}, "icons": {"style": "Line art, Monochromatic", "stroke": "1-2px solid neutral.baseBlack", "description": "图标设计简洁，采用单色线条风格，如文件夹、放大镜等。"}, "decorativeGraphics": {"style": "Abstract, Geometric", "elements": ["Wireframes", "Pixel-like patterns (e.g., grids of dots/plus signs)", "Simple geometric shapes"], "purpose": "用于填充空间和增强复古科技的主题感，不承载具体信息。"}}}}