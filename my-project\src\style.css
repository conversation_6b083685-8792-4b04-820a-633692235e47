/* Neo-Brutalist Design System - EDU邮箱平台 */

:root {
  /* Color Palette */
  --primary-background: #F9F900;
  --secondary-surface: #43C4B2;
  --accent-highlight: #FF00FF;
  --neutral-white: #FFFFFF;
  --neutral-black: #000000;

  /* Typography */
  --font-family: 'Roboto Mono', 'Source Code Pro', monospace, sans-serif;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;

  /* Border */
  --border-width: 2px;
  --border-radius: 8px;
  --shadow-offset: 3px;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  font-weight: var(--font-weight-normal);
  color: var(--neutral-black);
  background-color: var(--primary-background);
  line-height: 1.5;
  min-height: 100vh;
}

/* Typography Styles */
h1 {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--neutral-black);
  margin: 0 0 var(--spacing-md) 0;
  text-transform: none;
}

h2 {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--neutral-black);
  margin: 0 0 var(--spacing-sm) 0;
}

h3 {
  font-size: 1.5rem;
  font-weight: var(--font-weight-medium);
  color: var(--neutral-black);
  margin: 0 0 var(--spacing-sm) 0;
}

p {
  font-size: 1rem;
  font-weight: var(--font-weight-normal);
  color: var(--neutral-black);
  margin: 0 0 var(--spacing-sm) 0;
}

/* Button Component */
.btn {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-md);
  font-family: var(--font-family);
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  text-decoration: none;
  color: var(--neutral-black);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.btn:hover {
  transform: translate(-2px, -2px);
  box-shadow: calc(var(--shadow-offset) + 2px) calc(var(--shadow-offset) + 2px) 0 var(--neutral-black);
}

.btn:active {
  transform: translate(0, 0);
  box-shadow: 1px 1px 0 var(--neutral-black);
}

.btn-primary {
  background-color: var(--secondary-surface);
}

.btn-accent {
  background-color: var(--accent-highlight);
}

/* Input Field Component */
.input-field {
  width: 100%;
  padding: var(--spacing-sm);
  font-family: var(--font-family);
  font-size: 1rem;
  font-weight: var(--font-weight-normal);
  color: var(--neutral-black);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  outline: none;
  transition: all 0.2s ease;
}

.input-field:focus {
  transform: translate(-2px, -2px);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.input-field::placeholder {
  color: #666;
  font-style: italic;
}

/* Card Component */
.card {
  background-color: var(--secondary-surface);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  padding: var(--spacing-lg);
  margin: var(--spacing-sm) 0;
}

.card-white {
  background-color: var(--neutral-white);
}

/* Layout Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.text-center {
  text-align: center;
}

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }

/* App Layout */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
