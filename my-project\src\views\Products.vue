<template>
  <div class="products-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">EDU邮箱商店</h1>
        <p class="page-subtitle">选择您需要的高校EDU邮箱</p>
      </div>
      
      <!-- 筛选和搜索栏 -->
      <div class="filters-section">
        <div class="search-box">
          <input
            v-model="searchKeyword"
            type="text"
            class="input-field search-input"
            placeholder="搜索学校名称..."
            @keyup.enter="handleSearch"
          />
          <button @click="handleSearch" class="btn btn-primary search-btn">
            搜索
          </button>
        </div>
        
        <div class="filter-controls">
          <select v-model="sortBy" @change="handleSortChange" class="input-field sort-select">
            <option value="created_desc">最新上架</option>
            <option value="price_asc">价格从低到高</option>
            <option value="price_desc">价格从高到低</option>
          </select>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-section">
        <div class="loading-card">
          <p>正在加载商品...</p>
        </div>
      </div>
      
      <!-- 商品列表 -->
      <div v-else-if="products.length > 0" class="products-grid">
        <div
          v-for="product in products"
          :key="product.id"
          class="product-card"
          @click="goToProductDetail(product.id)"
        >
          <div class="product-image">
            <img :src="product.school_logo_url" :alt="product.name" />
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-description">{{ truncateText(product.description, 80) }}</p>
            <div class="product-meta">
              <span class="product-price">¥{{ product.price }}</span>
              <span class="product-warranty">{{ product.warranty_days }}天质保</span>
            </div>
            <div class="product-status">
              <span v-if="product.inventory.in_stock" class="status-badge in-stock">
                有货 ({{ product.inventory.available }})
              </span>
              <span v-else class="status-badge out-of-stock">
                缺货
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-card">
          <h3>暂无商品</h3>
          <p>{{ searchKeyword ? '没有找到相关商品，请尝试其他关键词' : '商品正在上架中，请稍后再来' }}</p>
          <button v-if="searchKeyword" @click="clearSearch" class="btn btn-primary">
            清除搜索
          </button>
        </div>
      </div>
      
      <!-- 分页 -->
      <div v-if="pagination && pagination.total_pages > 1" class="pagination-section">
        <div class="pagination">
          <button
            @click="goToPage(pagination.current_page - 1)"
            :disabled="!pagination.has_prev"
            class="btn pagination-btn"
          >
            上一页
          </button>
          
          <span class="pagination-info">
            第 {{ pagination.current_page }} 页，共 {{ pagination.total_pages }} 页
          </span>
          
          <button
            @click="goToPage(pagination.current_page + 1)"
            :disabled="!pagination.has_next"
            class="btn pagination-btn"
          >
            下一页
          </button>
        </div>
      </div>
      
      <!-- 错误提示 -->
      <div v-if="errorMessage" class="error-section">
        <div class="error-card">
          <p>{{ errorMessage }}</p>
          <button @click="loadProducts" class="btn btn-primary">重试</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { productService } from '../services/product'
import { useToast } from '../composables/useToast'

export default {
  name: 'Products',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const { error } = useToast()
    
    // 响应式数据
    const products = ref([])
    const pagination = ref(null)
    const isLoading = ref(false)
    const errorMessage = ref('')
    const searchKeyword = ref('')
    const sortBy = ref('created_desc')
    const currentPage = ref(1)
    
    // 方法
    const loadProducts = async (page = 1) => {
      isLoading.value = true
      errorMessage.value = ''
      
      try {
        const params = {
          page,
          limit: 12,
          sort: sortBy.value,
          status: 'active'
        }
        
        if (searchKeyword.value) {
          params.search = searchKeyword.value
        }
        
        const response = await productService.getProducts(params)
        products.value = response.products
        pagination.value = response.pagination
        currentPage.value = page
      } catch (err) {
        errorMessage.value = err.message || '加载商品失败'
        error(err.message || '加载商品失败')
      } finally {
        isLoading.value = false
      }
    }
    
    const handleSearch = () => {
      currentPage.value = 1
      loadProducts(1)
    }
    
    const handleSortChange = () => {
      currentPage.value = 1
      loadProducts(1)
    }
    
    const clearSearch = () => {
      searchKeyword.value = ''
      currentPage.value = 1
      loadProducts(1)
    }
    
    const goToPage = (page) => {
      if (page >= 1 && page <= pagination.value.total_pages) {
        loadProducts(page)
      }
    }
    
    const goToProductDetail = (productId) => {
      router.push(`/products/${productId}`)
    }
    
    const truncateText = (text, maxLength) => {
      if (!text) return ''
      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
    }
    
    // 监听路由变化
    watch(() => route.query, () => {
      if (route.query.search) {
        searchKeyword.value = route.query.search
      }
      loadProducts(1)
    })
    
    // 组件挂载时加载数据
    onMounted(() => {
      if (route.query.search) {
        searchKeyword.value = route.query.search
      }
      loadProducts(1)
    })
    
    return {
      products,
      pagination,
      isLoading,
      errorMessage,
      searchKeyword,
      sortBy,
      currentPage,
      loadProducts,
      handleSearch,
      handleSortChange,
      clearSearch,
      goToPage,
      goToProductDetail,
      truncateText
    }
  }
}
</script>

<style scoped>
.products-page {
  min-height: calc(100vh - 80px);
  padding: var(--spacing-lg) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
}

.filters-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.search-box {
  display: flex;
  gap: var(--spacing-sm);
  flex: 1;
  max-width: 400px;
}

.search-input {
  flex: 1;
}

.search-btn {
  white-space: nowrap;
}

.filter-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.sort-select {
  min-width: 150px;
}

.loading-section,
.empty-state,
.error-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.loading-card,
.empty-card,
.error-card {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.product-card {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.product-card:hover {
  transform: translate(-2px, -2px);
  box-shadow: calc(var(--shadow-offset) + 2px) calc(var(--shadow-offset) + 2px) 0 var(--neutral-black);
}

.product-image {
  height: 200px;
  background-color: var(--secondary-surface);
  border-bottom: var(--border-width) solid var(--neutral-black);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: var(--spacing-lg);
}

.product-name {
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--neutral-black);
}

.product-description {
  font-size: 0.9rem;
  color: #666;
  margin: 0 0 var(--spacing-md) 0;
  line-height: 1.4;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.product-price {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--accent-highlight);
}

.product-warranty {
  font-size: 0.8rem;
  color: #666;
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.product-status {
  display: flex;
  justify-content: flex-end;
}

.status-badge {
  font-size: 0.8rem;
  font-weight: var(--font-weight-medium);
  padding: 4px 8px;
  border-radius: 4px;
  border: var(--border-width) solid var(--neutral-black);
  text-transform: uppercase;
}

.status-badge.in-stock {
  background-color: var(--secondary-surface);
  color: var(--neutral-black);
}

.status-badge.out-of-stock {
  background-color: #ffebee;
  color: #c62828;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
}

.pagination {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.pagination-btn {
  padding: var(--spacing-sm) var(--spacing-md);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.pagination-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.pagination-info {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-black);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filters-section {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .search-box {
    max-width: none;
    width: 100%;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .page-title {
    font-size: 2rem;
  }

  .pagination {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .product-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .search-box {
    flex-direction: column;
  }

  .search-btn {
    width: 100%;
  }
}
</style>
