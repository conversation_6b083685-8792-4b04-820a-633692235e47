<template>
  <div class="product-detail-page">
    <div class="container">
      <!-- 返回按钮 -->
      <div class="back-section">
        <button @click="goBack" class="btn back-btn">
          ← 返回商品列表
        </button>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-section">
        <div class="loading-card">
          <p>正在加载商品详情...</p>
        </div>
      </div>
      
      <!-- 商品详情 -->
      <div v-else-if="product" class="product-detail">
        <div class="product-main">
          <div class="product-image-section">
            <div class="product-image-container">
              <img :src="product.school_logo_url" :alt="product.name" class="product-image" />
            </div>
          </div>
          
          <div class="product-info-section">
            <div class="product-header">
              <h1 class="product-title">{{ product.name }}</h1>
              <div class="product-status">
                <span v-if="product.inventory.in_stock" class="status-badge in-stock">
                  有货 ({{ product.inventory.available }}件)
                </span>
                <span v-else class="status-badge out-of-stock">
                  暂时缺货
                </span>
              </div>
            </div>
            
            <div class="product-price-section">
              <span class="product-price">¥{{ product.price }}</span>
              <span class="product-warranty">{{ product.warranty_days }}天质保</span>
            </div>
            
            <div class="product-description">
              <h3>商品描述</h3>
              <p>{{ product.description }}</p>
            </div>
            
            <div class="product-inventory">
              <h3>库存信息</h3>
              <div class="inventory-details">
                <div class="inventory-item">
                  <span class="label">总库存:</span>
                  <span class="value">{{ product.inventory.total }}件</span>
                </div>
                <div class="inventory-item">
                  <span class="label">可售:</span>
                  <span class="value">{{ product.inventory.available }}件</span>
                </div>
                <div class="inventory-item">
                  <span class="label">已售:</span>
                  <span class="value">{{ product.inventory.sold || 0 }}件</span>
                </div>
              </div>
            </div>
            
            <div class="product-actions">
              <button
                v-if="product.inventory.in_stock"
                @click="handlePurchase"
                class="btn btn-primary purchase-btn"
                :disabled="!isAuthenticated"
              >
                {{ isAuthenticated ? '立即购买' : '请先登录' }}
              </button>
              <button
                v-else
                class="btn purchase-btn"
                disabled
              >
                暂时缺货
              </button>
              
              <button @click="addToCart" class="btn btn-accent cart-btn" :disabled="!product.inventory.in_stock || !isAuthenticated">
                加入购物车
              </button>
            </div>
            
            <div v-if="!isAuthenticated" class="login-prompt">
              <p>
                <router-link to="/login" class="link">登录</router-link>
                或
                <router-link to="/register" class="link">注册</router-link>
                后即可购买
              </p>
            </div>
          </div>
        </div>
        
        <!-- 商品详细信息 -->
        <div class="product-details-section">
          <div class="details-card">
            <h2>商品详细信息</h2>
            <div class="details-grid">
              <div class="detail-item">
                <span class="detail-label">商品名称:</span>
                <span class="detail-value">{{ product.name }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">售价:</span>
                <span class="detail-value">¥{{ product.price }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">质保期:</span>
                <span class="detail-value">{{ product.warranty_days }}天</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">商品状态:</span>
                <span class="detail-value">{{ product.status === 'active' ? '正常销售' : '暂停销售' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">上架时间:</span>
                <span class="detail-value">{{ formatDate(product.created_at) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">更新时间:</span>
                <span class="detail-value">{{ formatDate(product.updated_at) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else class="error-section">
        <div class="error-card">
          <h3>商品不存在</h3>
          <p>{{ errorMessage || '您访问的商品不存在或已下架' }}</p>
          <button @click="goBack" class="btn btn-primary">返回商品列表</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { useToast } from '../composables/useToast'
import { productService } from '../services/product'

export default {
  name: 'ProductDetail',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const { isAuthenticated } = useAuth()
    const { success, error } = useToast()
    
    // 响应式数据
    const product = ref(null)
    const isLoading = ref(false)
    const errorMessage = ref('')
    
    // 方法
    const loadProduct = async () => {
      isLoading.value = true
      errorMessage.value = ''
      
      try {
        const productData = await productService.getProductById(props.id)
        product.value = productData
      } catch (err) {
        errorMessage.value = err.message || '加载商品详情失败'
        error(err.message || '加载商品详情失败')
      } finally {
        isLoading.value = false
      }
    }
    
    const goBack = () => {
      router.push('/products')
    }
    
    const handlePurchase = () => {
      if (!isAuthenticated.value) {
        error('请先登录后再购买')
        return
      }
      
      // TODO: 实现购买逻辑
      success('购买功能将在后续版本中实现')
    }
    
    const addToCart = () => {
      if (!isAuthenticated.value) {
        error('请先登录后再添加到购物车')
        return
      }
      
      // TODO: 实现购物车逻辑
      success('购物车功能将在后续版本中实现')
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadProduct()
    })
    
    return {
      product,
      isLoading,
      errorMessage,
      isAuthenticated,
      loadProduct,
      goBack,
      handlePurchase,
      addToCart,
      formatDate
    }
  }
}
</script>

<style scoped>
.product-detail-page {
  min-height: calc(100vh - 80px);
  padding: var(--spacing-lg) 0;
}

.back-section {
  margin-bottom: var(--spacing-lg);
}

.back-btn {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
}

.loading-section,
.error-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-card,
.error-card {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.product-detail {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.product-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
}

.product-image-section {
  background-color: var(--secondary-surface);
  border-right: var(--border-width) solid var(--neutral-black);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.product-image-container {
  width: 100%;
  max-width: 400px;
  aspect-ratio: 1;
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  overflow: hidden;
  background-color: var(--neutral-white);
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info-section {
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.product-title {
  font-size: 2rem;
  margin: 0;
  flex: 1;
}

.status-badge {
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  border: var(--border-width) solid var(--neutral-black);
  text-transform: uppercase;
  white-space: nowrap;
}

.status-badge.in-stock {
  background-color: var(--secondary-surface);
  color: var(--neutral-black);
}

.status-badge.out-of-stock {
  background-color: #ffebee;
  color: #c62828;
}

.product-price-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.product-price {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--accent-highlight);
}

.product-warranty {
  font-size: 1rem;
  color: #666;
  background-color: #f5f5f5;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  border: 1px solid #ddd;
}

.product-description h3,
.product-inventory h3 {
  font-size: 1.2rem;
  margin: 0 0 var(--spacing-sm) 0;
  font-weight: var(--font-weight-bold);
}

.product-description p {
  margin: 0;
  line-height: 1.6;
  color: #333;
}

.inventory-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
}

.inventory-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.inventory-item .label {
  font-size: 0.9rem;
  color: #666;
}

.inventory-item .value {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-black);
}

.product-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.purchase-btn,
.cart-btn {
  flex: 1;
  padding: var(--spacing-md);
  font-size: 1.1rem;
}

.purchase-btn:disabled,
.cart-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.purchase-btn:disabled:hover,
.cart-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.login-prompt {
  text-align: center;
  padding: var(--spacing-md);
  background-color: #f8f9fa;
  border: var(--border-width) solid #dee2e6;
  border-radius: var(--border-radius);
}

.login-prompt p {
  margin: 0;
  color: #666;
}

.link {
  color: var(--neutral-black);
  text-decoration: underline;
  font-weight: var(--font-weight-medium);
}

.link:hover {
  color: var(--accent-highlight);
}

.product-details-section {
  margin-top: var(--spacing-lg);
}

.details-card {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  padding: var(--spacing-xl);
}

.details-card h2 {
  font-size: 1.5rem;
  margin: 0 0 var(--spacing-lg) 0;
  font-weight: var(--font-weight-bold);
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius);
}

.detail-label {
  font-weight: var(--font-weight-medium);
  color: #666;
}

.detail-value {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-black);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .product-main {
    grid-template-columns: 1fr;
  }

  .product-image-section {
    border-right: none;
    border-bottom: var(--border-width) solid var(--neutral-black);
  }

  .product-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 768px) {
  .product-actions {
    flex-direction: column;
  }

  .product-price-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .product-price {
    font-size: 2rem;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .product-info-section {
    padding: var(--spacing-md);
  }

  .product-title {
    font-size: 1.5rem;
  }

  .inventory-details {
    grid-template-columns: 1fr;
  }
}
</style>
