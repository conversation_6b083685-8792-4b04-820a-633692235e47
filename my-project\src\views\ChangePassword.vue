<template>
  <div class="change-password-page">
    <div class="container">
      <!-- 返回按钮 -->
      <div class="back-section">
        <button @click="goBack" class="btn back-btn">
          ← 返回用户中心
        </button>
      </div>
      
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">修改密码</h1>
        <p class="page-subtitle">为了您的账户安全，请定期更换密码</p>
      </div>
      
      <!-- 未登录提示 -->
      <div v-if="!isAuthenticated" class="login-prompt-section">
        <div class="login-prompt-card">
          <h3>请先登录</h3>
          <p>登录后才能修改密码</p>
          <div class="login-actions">
            <router-link to="/login" class="btn btn-primary">登录</router-link>
            <router-link to="/register" class="btn btn-accent">注册</router-link>
          </div>
        </div>
      </div>
      
      <!-- 修改密码表单 -->
      <div v-else class="password-form-section">
        <div class="password-form-card">
          <div class="card-header">
            <h2>修改密码</h2>
          </div>
          
          <form @submit.prevent="handleChangePassword" class="password-form">
            <div class="form-group">
              <label for="currentPassword" class="form-label">当前密码 *</label>
              <input
                id="currentPassword"
                v-model="passwordForm.current_password"
                type="password"
                class="input-field"
                placeholder="请输入当前密码"
                required
                :disabled="isUpdating"
              />
            </div>
            
            <div class="form-group">
              <label for="newPassword" class="form-label">新密码 *</label>
              <input
                id="newPassword"
                v-model="passwordForm.new_password"
                type="password"
                class="input-field"
                placeholder="请输入新密码"
                required
                :disabled="isUpdating"
              />
              <p class="form-help">密码长度至少为6位字符</p>
            </div>
            
            <div class="form-group">
              <label for="confirmPassword" class="form-label">确认新密码 *</label>
              <input
                id="confirmPassword"
                v-model="passwordForm.confirm_password"
                type="password"
                class="input-field"
                placeholder="请再次输入新密码"
                required
                :disabled="isUpdating"
              />
              <p v-if="passwordForm.new_password && passwordForm.confirm_password && !passwordsMatch" class="form-error">
                两次输入的密码不一致
              </p>
            </div>
            
            <div class="form-actions">
              <button
                type="button"
                @click="resetForm"
                class="btn reset-btn"
                :disabled="isUpdating"
              >
                重置
              </button>
              <button
                type="submit"
                class="btn btn-primary update-btn"
                :disabled="!isFormValid || isUpdating"
              >
                {{ isUpdating ? '修改中...' : '修改密码' }}
              </button>
            </div>
          </form>
          
          <!-- 错误提示 -->
          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
          
          <!-- 成功提示 -->
          <div v-if="successMessage" class="success-message">
            {{ successMessage }}
          </div>
        </div>
        
        <!-- 安全提示 -->
        <div class="security-tips-card">
          <div class="card-header">
            <h2>安全提示</h2>
          </div>
          
          <div class="tips-content">
            <div class="tip-item">
              <div class="tip-icon">🔒</div>
              <div class="tip-text">
                <h4>密码强度</h4>
                <p>建议使用包含字母、数字和特殊字符的复杂密码</p>
              </div>
            </div>
            
            <div class="tip-item">
              <div class="tip-icon">🔄</div>
              <div class="tip-text">
                <h4>定期更换</h4>
                <p>建议每3-6个月更换一次密码，提高账户安全性</p>
              </div>
            </div>
            
            <div class="tip-item">
              <div class="tip-icon">🚫</div>
              <div class="tip-text">
                <h4>避免重复</h4>
                <p>不要在多个网站使用相同的密码</p>
              </div>
            </div>
            
            <div class="tip-item">
              <div class="tip-icon">👀</div>
              <div class="tip-text">
                <h4>保护隐私</h4>
                <p>不要在公共场所输入密码，注意防范他人偷窥</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { useUserCenter } from '../composables/useUserCenter'
import { useToast } from '../composables/useToast'

export default {
  name: 'ChangePassword',
  setup() {
    const router = useRouter()
    const { isAuthenticated } = useAuth()
    const { changePassword } = useUserCenter()
    const { success, error } = useToast()
    
    // 响应式数据
    const passwordForm = ref({
      current_password: '',
      new_password: '',
      confirm_password: ''
    })
    
    const isUpdating = ref(false)
    const errorMessage = ref('')
    const successMessage = ref('')
    
    // 计算属性
    const passwordsMatch = computed(() => {
      return passwordForm.value.new_password === passwordForm.value.confirm_password
    })
    
    const isFormValid = computed(() => {
      return (
        passwordForm.value.current_password &&
        passwordForm.value.new_password &&
        passwordForm.value.confirm_password &&
        passwordForm.value.new_password.length >= 6 &&
        passwordsMatch.value
      )
    })
    
    // 方法
    const goBack = () => {
      router.push('/user')
    }
    
    const resetForm = () => {
      passwordForm.value = {
        current_password: '',
        new_password: '',
        confirm_password: ''
      }
      errorMessage.value = ''
      successMessage.value = ''
    }
    
    const handleChangePassword = async () => {
      if (!isFormValid.value) {
        errorMessage.value = '请填写正确的密码信息'
        return
      }
      
      isUpdating.value = true
      errorMessage.value = ''
      successMessage.value = ''
      
      try {
        await changePassword({
          current_password: passwordForm.value.current_password,
          new_password: passwordForm.value.new_password,
          confirm_password: passwordForm.value.confirm_password
        })
        
        successMessage.value = '密码修改成功！请使用新密码登录'
        success('密码修改成功！')
        
        // 清空表单
        resetForm()
        
        // 3秒后跳转到用户中心
        setTimeout(() => {
          router.push('/user')
        }, 3000)
        
      } catch (err) {
        errorMessage.value = err.message || '修改密码失败，请重试'
        error(err.message || '修改密码失败，请重试')
      } finally {
        isUpdating.value = false
      }
    }
    
    return {
      isAuthenticated,
      passwordForm,
      isUpdating,
      errorMessage,
      successMessage,
      passwordsMatch,
      isFormValid,
      goBack,
      resetForm,
      handleChangePassword
    }
  }
}
</script>

<style scoped>
.change-password-page {
  min-height: calc(100vh - 80px);
  padding: var(--spacing-lg) 0;
}

.back-section {
  margin-bottom: var(--spacing-lg);
}

.back-btn {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
}

.login-prompt-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.login-prompt-card {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  max-width: 400px;
}

.login-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-lg);
}

.password-form-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  max-width: 1000px;
  margin: 0 auto;
}

.password-form-card,
.security-tips-card {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-lg);
  background-color: var(--secondary-surface);
  border-bottom: var(--border-width) solid var(--neutral-black);
}

.card-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.password-form {
  padding: var(--spacing-lg);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  font-size: 0.9rem;
}

.form-help {
  font-size: 0.8rem;
  color: #666;
  margin: var(--spacing-xs) 0 0 0;
  line-height: 1.4;
}

.form-error {
  font-size: 0.8rem;
  color: #c62828;
  margin: var(--spacing-xs) 0 0 0;
  line-height: 1.4;
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

.reset-btn {
  flex: 1;
  padding: var(--spacing-md);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
}

.update-btn {
  flex: 2;
  padding: var(--spacing-md);
  font-size: 1.1rem;
}

.update-btn:disabled,
.reset-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.update-btn:disabled:hover,
.reset-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: var(--spacing-sm);
  border: var(--border-width) solid #c62828;
  border-radius: var(--border-radius);
  margin-top: var(--spacing-md);
  font-size: 0.9rem;
  text-align: center;
}

.success-message {
  background-color: #e8f5e8;
  color: #2e7d32;
  padding: var(--spacing-sm);
  border: var(--border-width) solid #2e7d32;
  border-radius: var(--border-radius);
  margin-top: var(--spacing-md);
  font-size: 0.9rem;
  text-align: center;
}

.tips-content {
  padding: var(--spacing-lg);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius);
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--secondary-surface);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  flex-shrink: 0;
}

.tip-text h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1rem;
  font-weight: var(--font-weight-bold);
}

.tip-text p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .password-form-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
  }

  .tip-item {
    flex-direction: column;
    text-align: center;
  }

  .login-actions {
    flex-direction: column;
  }

  .page-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .password-form-section {
    margin: 0 var(--spacing-md);
  }

  .tip-icon {
    width: 35px;
    height: 35px;
    font-size: 1.2rem;
  }
}
</style>
