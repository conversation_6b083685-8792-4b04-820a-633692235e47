-- ----------------------------
-- 使用 InnoDB 引擎并设置默认字符集
-- ----------------------------
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户唯一ID',
  `username` varchar(50) NOT NULL COMMENT '用户名，用于登录',
  `password_hash` varchar(255) NOT NULL COMMENT '加密后的密码哈希',
  `email` varchar(100) NOT NULL COMMENT '注册邮箱，用于登录和联系',
  `role` enum('customer','admin') NOT NULL DEFAULT 'customer' COMMENT '用户角色 (customer: 注册用户, admin: 管理员)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '账号创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '账号信息更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '商品唯一ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称 (如: 牛津大学EDU邮箱)',
  `school_logo_url` varchar(255) DEFAULT NULL COMMENT '学校Logo图片地址',
  `description` text COMMENT '商品详细描述 (包含权益、注意事项等)',
  `price` decimal(10,2) NOT NULL COMMENT '商品售价',
  `warranty_days` int(11) NOT NULL DEFAULT '0' COMMENT '质保天数 (0表示无质保)',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '商品状态 (active: 在售, inactive: 已下架)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '商品创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '商品信息更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- ----------------------------
-- Table structure for product_inventory
-- ----------------------------
DROP TABLE IF EXISTS `product_inventory`;
CREATE TABLE `product_inventory` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '库存唯一ID',
  `product_id` int(10) unsigned NOT NULL COMMENT '关联的商品ID (外键)',
  `email_account` varchar(255) NOT NULL COMMENT 'EDU邮箱账号 (建议在后端加密存储)',
  `email_password` varchar(255) NOT NULL COMMENT 'EDU邮箱密码 (建议在后端加密存储)',
  `status` enum('available','sold','reserved') NOT NULL DEFAULT 'available' COMMENT '库存状态 (available: 可用, sold: 已售, reserved: 预留)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '状态更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id_status` (`product_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品库存表';

-- ----------------------------
-- Table structure for shopping_cart_items
-- ----------------------------
DROP TABLE IF EXISTS `shopping_cart_items`;
CREATE TABLE `shopping_cart_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '购物车项唯一ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID (外键)',
  `product_id` int(10) unsigned NOT NULL COMMENT '商品ID (外键)',
  `quantity` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '购买数量 (此业务中通常为1)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`user_id`,`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车项表';

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单唯一ID',
  `order_number` varchar(32) NOT NULL COMMENT '业务订单号 (由后端生成，便于查询)',
  `user_id` int(10) unsigned NOT NULL COMMENT '下单用户ID (外键)',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `receiving_email` varchar(100) NOT NULL COMMENT '用于接收商品账号密码的邮箱',
  `status` enum('pending_payment','completed','cancelled') NOT NULL DEFAULT 'pending_payment' COMMENT '订单状态',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式 (如: alipay, wechat_pay)',
  `payment_time` timestamp NULL DEFAULT NULL COMMENT '支付成功时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '订单更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_number` (`order_number`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- ----------------------------
-- Table structure for order_items
-- ----------------------------
DROP TABLE IF EXISTS `order_items`;
CREATE TABLE `order_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单详情唯一ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID (外键)',
  `product_id` int(10) unsigned NOT NULL COMMENT '商品ID (外键)',
  `inventory_id` bigint(20) unsigned DEFAULT NULL COMMENT '发货的库存ID (外键)，支付成功后回填',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称快照，防止商品信息变更',
  `price` decimal(10,2) NOT NULL COMMENT '成交价格快照',
  `quantity` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '购买数量',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_inventory_id` (`inventory_id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单详情表';

SET FOREIGN_KEY_CHECKS = 1;