import api from './api'

// 认证服务
export const authService = {
  // 用户登录
  async login(username, password) {
    try {
      const response = await api.post('/auth/login', {
        username,
        password
      })
      
      if (response.success) {
        const { user, token } = response.data
        // 存储token和用户信息
        localStorage.setItem('token', token)
        localStorage.setItem('user', JSON.stringify(user))
        return { user, token }
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 用户注册
  async register(username, email, password) {
    try {
      const response = await api.post('/auth/register', {
        username,
        email,
        password
      })
      
      if (response.success) {
        const { user, token } = response.data
        // 存储token和用户信息
        localStorage.setItem('token', token)
        localStorage.setItem('user', JSON.stringify(user))
        return { user, token }
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 验证token
  async verifyToken() {
    try {
      const response = await api.get('/auth/verify')
      
      if (response.success) {
        const { user } = response.data
        // 更新本地用户信息
        localStorage.setItem('user', JSON.stringify(user))
        return user
      } else {
        throw new Error('Token验证失败')
      }
    } catch (error) {
      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      throw error
    }
  },

  // 用户登出
  async logout() {
    try {
      await api.post('/auth/logout')
    } catch (error) {
      console.warn('登出请求失败:', error.message)
    } finally {
      // 无论请求是否成功，都清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    }
  },

  // 获取当前用户信息
  getCurrentUser() {
    try {
      const userStr = localStorage.getItem('user')
      return userStr ? JSON.parse(userStr) : null
    } catch (error) {
      console.error('解析用户信息失败:', error)
      return null
    }
  },

  // 检查是否已登录
  isAuthenticated() {
    const token = localStorage.getItem('token')
    const user = this.getCurrentUser()
    return !!(token && user)
  },

  // 获取token
  getToken() {
    return localStorage.getItem('token')
  }
}

export default authService
