import api from './api'

// 购物车服务
export const cartService = {
  // 添加商品到购物车
  async addToCart(productId, quantity = 1) {
    try {
      if (!productId || productId <= 0) {
        throw new Error('商品ID无效')
      }
      
      if (quantity < 1 || quantity > 10) {
        throw new Error('商品数量必须在1-10之间')
      }
      
      const response = await api.post('/cart', {
        product_id: productId,
        quantity
      })
      
      if (response.success) {
        return response.data.cart_item
      } else {
        throw new Error(response.message || '添加到购物车失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 获取购物车列表
  async getCartItems() {
    try {
      const response = await api.get('/cart')
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || '获取购物车失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 更新购物车商品数量
  async updateCartItem(cartItemId, quantity) {
    try {
      if (!cartItemId || cartItemId <= 0) {
        throw new Error('购物车项ID无效')
      }
      
      if (quantity < 1 || quantity > 10) {
        throw new Error('商品数量必须在1-10之间')
      }
      
      const response = await api.put(`/cart/${cartItemId}`, {
        quantity
      })
      
      if (response.success) {
        return response.data.cart_item
      } else {
        throw new Error(response.message || '更新购物车失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 删除购物车商品
  async removeCartItem(cartItemId) {
    try {
      if (!cartItemId || cartItemId <= 0) {
        throw new Error('购物车项ID无效')
      }
      
      const response = await api.delete(`/cart/${cartItemId}`)
      
      if (response.success) {
        return true
      } else {
        throw new Error(response.message || '删除购物车商品失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 清空购物车
  async clearCart() {
    try {
      const response = await api.delete('/cart')
      
      if (response.success) {
        return response.data.deleted_count || 0
      } else {
        throw new Error(response.message || '清空购物车失败')
      }
    } catch (error) {
      throw error
    }
  }
}

export default cartService
