<template>
  <div class="user-center-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">用户中心</h1>
        <p class="page-subtitle">管理您的账户和订单</p>
      </div>
      
      <!-- 未登录提示 -->
      <div v-if="!isAuthenticated" class="login-prompt-section">
        <div class="login-prompt-card">
          <h3>请先登录</h3>
          <p>登录后即可查看您的用户中心</p>
          <div class="login-actions">
            <router-link to="/login" class="btn btn-primary">登录</router-link>
            <router-link to="/register" class="btn btn-accent">注册</router-link>
          </div>
        </div>
      </div>
      
      <!-- 用户中心内容 -->
      <div v-else class="user-center-content">
        <!-- 用户信息卡片 -->
        <div class="user-info-section">
          <div class="user-info-card">
            <div class="card-header">
              <h2>个人信息</h2>
              <router-link to="/user/profile" class="btn btn-accent edit-btn">
                编辑资料
              </router-link>
            </div>
            
            <div class="user-details">
              <div class="user-avatar">
                <div class="avatar-placeholder">
                  {{ user?.username?.charAt(0)?.toUpperCase() || 'U' }}
                </div>
              </div>
              
              <div class="user-info">
                <h3 class="username">{{ user?.username || '未设置' }}</h3>
                <p class="user-email">{{ user?.email || '未设置' }}</p>
                <p class="user-role">{{ getRoleText(user?.role) }}</p>
                <p class="join-date">
                  加入时间: {{ formatDate(userProfile?.created_at) }}
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="stats-section">
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">📦</div>
              <div class="stat-info">
                <h3 class="stat-number">{{ userStats.total_orders }}</h3>
                <p class="stat-label">总订单数</p>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">✅</div>
              <div class="stat-info">
                <h3 class="stat-number">{{ userStats.completed_orders }}</h3>
                <p class="stat-label">已完成订单</p>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">⏳</div>
              <div class="stat-info">
                <h3 class="stat-number">{{ userStats.pending_orders }}</h3>
                <p class="stat-label">待支付订单</p>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">💰</div>
              <div class="stat-info">
                <h3 class="stat-number">¥{{ userStats.total_spent }}</h3>
                <p class="stat-label">累计消费</p>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">📧</div>
              <div class="stat-info">
                <h3 class="stat-number">{{ userStats.total_accounts }}</h3>
                <p class="stat-label">已购买账号</p>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <h3 class="stat-number">{{ completionRate }}%</h3>
                <p class="stat-label">订单完成率</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="quick-actions-section">
          <div class="quick-actions-card">
            <div class="card-header">
              <h2>快捷操作</h2>
            </div>
            
            <div class="actions-grid">
              <router-link to="/orders" class="action-item">
                <div class="action-icon">📋</div>
                <div class="action-info">
                  <h3>我的订单</h3>
                  <p>查看所有订单记录</p>
                </div>
              </router-link>
              
              <router-link to="/products" class="action-item">
                <div class="action-icon">🛒</div>
                <div class="action-info">
                  <h3>继续购物</h3>
                  <p>浏览更多EDU邮箱</p>
                </div>
              </router-link>
              
              <router-link to="/cart" class="action-item">
                <div class="action-icon">🛍️</div>
                <div class="action-info">
                  <h3>购物车</h3>
                  <p>查看购物车商品</p>
                </div>
              </router-link>
              
              <router-link to="/user/password" class="action-item">
                <div class="action-icon">🔒</div>
                <div class="action-info">
                  <h3>修改密码</h3>
                  <p>更改账户密码</p>
                </div>
              </router-link>
            </div>
          </div>
        </div>
        
        <!-- 最近订单 -->
        <div class="recent-orders-section">
          <div class="recent-orders-card">
            <div class="card-header">
              <h2>最近订单</h2>
              <router-link to="/orders" class="btn btn-accent view-all-btn">
                查看全部
              </router-link>
            </div>
            
            <div v-if="isLoadingOrders" class="loading-orders">
              <p>正在加载订单...</p>
            </div>
            
            <div v-else-if="recentOrders.length > 0" class="orders-list">
              <div
                v-for="order in recentOrders"
                :key="order.id"
                class="order-item"
                @click="goToOrderDetail(order.order_number)"
              >
                <div class="order-info">
                  <h4 class="order-number">{{ order.order_number }}</h4>
                  <p class="order-date">{{ formatDate(order.created_at) }}</p>
                </div>
                <div class="order-status" :class="`status-${order.status}`">
                  {{ getStatusText(order.status) }}
                </div>
                <div class="order-amount">¥{{ order.total_amount }}</div>
              </div>
            </div>
            
            <div v-else class="no-orders">
              <p>暂无订单记录</p>
              <router-link to="/products" class="btn btn-primary">去购物</router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { useUserCenter } from '../composables/useUserCenter'
import { useToast } from '../composables/useToast'

export default {
  name: 'UserCenter',
  setup() {
    const router = useRouter()
    const { isAuthenticated, user } = useAuth()
    const {
      userProfile,
      userStats,
      completionRate,
      loadUserProfile,
      loadUserStats,
      getUserOrders
    } = useUserCenter()
    const { error } = useToast()
    
    // 响应式数据
    const recentOrders = ref([])
    const isLoadingOrders = ref(false)
    
    // 方法
    const loadRecentOrders = async () => {
      if (!isAuthenticated.value) return
      
      isLoadingOrders.value = true
      try {
        const ordersData = await getUserOrders(1, 5) // 获取最近5个订单
        recentOrders.value = ordersData.orders || []
      } catch (err) {
        console.error('加载最近订单失败:', err)
        recentOrders.value = []
      } finally {
        isLoadingOrders.value = false
      }
    }
    
    const goToOrderDetail = (orderNumber) => {
      router.push(`/orders/${orderNumber}`)
    }
    
    const getRoleText = (role) => {
      const roleMap = {
        'customer': '普通用户',
        'admin': '管理员'
      }
      return roleMap[role] || '用户'
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        'pending_payment': '待支付',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
    
    // 组件挂载时加载数据
    onMounted(async () => {
      if (isAuthenticated.value) {
        await Promise.all([
          loadUserProfile(),
          loadUserStats(),
          loadRecentOrders()
        ])
      }
    })
    
    return {
      isAuthenticated,
      user,
      userProfile,
      userStats,
      completionRate,
      recentOrders,
      isLoadingOrders,
      goToOrderDetail,
      getRoleText,
      getStatusText,
      formatDate
    }
  }
}
</script>

<style scoped>
.user-center-page {
  min-height: calc(100vh - 80px);
  padding: var(--spacing-lg) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
}

.login-prompt-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.login-prompt-card {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  max-width: 400px;
}

.login-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-lg);
}

.user-center-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
}

.user-info-section,
.stats-section,
.quick-actions-section,
.recent-orders-section {
  grid-column: 1 / -1;
}

.user-info-card,
.quick-actions-card,
.recent-orders-card {
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--secondary-surface);
  border-bottom: var(--border-width) solid var(--neutral-black);
}

.card-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.edit-btn,
.view-all-btn {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 0.9rem;
}

.user-details {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

.user-avatar {
  flex-shrink: 0;
}

.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--accent-highlight);
  color: var(--neutral-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  border: var(--border-width) solid var(--neutral-black);
}

.user-info {
  flex: 1;
}

.username {
  font-size: 1.5rem;
  margin: 0 0 var(--spacing-xs) 0;
  font-weight: var(--font-weight-bold);
}

.user-email,
.user-role,
.join-date {
  margin: 0 0 var(--spacing-xs) 0;
  color: #666;
  font-size: 0.9rem;
}

.user-role {
  color: var(--accent-highlight);
  font-weight: var(--font-weight-medium);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translate(-1px, -1px);
  box-shadow: calc(var(--shadow-offset) + 1px) calc(var(--shadow-offset) + 1px) 0 var(--neutral-black);
}

.stat-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--secondary-surface);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--accent-highlight);
}

.stat-label {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}

.action-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  text-decoration: none;
  color: var(--neutral-black);
  transition: all 0.2s ease;
}

.action-item:hover {
  transform: translate(-2px, -2px);
  box-shadow: calc(var(--shadow-offset) + 2px) calc(var(--shadow-offset) + 2px) 0 var(--neutral-black);
  color: var(--neutral-black);
}

.action-icon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--secondary-surface);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
}

.action-info h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
}

.action-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.loading-orders,
.no-orders {
  padding: var(--spacing-xl);
  text-align: center;
  color: #666;
}

.orders-list {
  padding: var(--spacing-lg);
}

.order-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: var(--spacing-md);
  align-items: center;
  padding: var(--spacing-md);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  background-color: var(--neutral-white);
  cursor: pointer;
  transition: all 0.2s ease;
}

.order-item:hover {
  transform: translate(-1px, -1px);
  box-shadow: 2px 2px 0 var(--neutral-black);
}

.order-item:last-child {
  margin-bottom: 0;
}

.order-info h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1rem;
  font-weight: var(--font-weight-bold);
}

.order-date {
  margin: 0;
  font-size: 0.8rem;
  color: #666;
}

.order-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  border: var(--border-width) solid var(--neutral-black);
  font-size: 0.8rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.status-pending_payment {
  background-color: #fff3cd;
  color: #856404;
}

.status-completed {
  background-color: #d4edda;
  color: #155724;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.order-amount {
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
  color: var(--accent-highlight);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .user-center-content {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .user-details {
    flex-direction: column;
    text-align: center;
  }

  .card-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .order-item {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .login-actions {
    flex-direction: column;
  }

  .page-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .avatar-placeholder {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>
