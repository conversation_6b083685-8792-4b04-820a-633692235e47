import { ref, computed } from 'vue'
import { authService } from '../services/auth'

// 全局用户状态
const user = ref(null)
const isLoading = ref(false)

// 初始化用户状态
const initializeAuth = () => {
  const currentUser = authService.getCurrentUser()
  if (currentUser) {
    user.value = currentUser
  }
}

// 初始化
initializeAuth()

export function useAuth() {
  // 计算属性
  const isAuthenticated = computed(() => !!user.value)
  
  // 登录
  const login = async (username, password) => {
    isLoading.value = true
    try {
      const result = await authService.login(username, password)
      user.value = result.user
      return result
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (username, email, password) => {
    isLoading.value = true
    try {
      const result = await authService.register(username, email, password)
      user.value = result.user
      return result
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    isLoading.value = true
    try {
      await authService.logout()
      user.value = null
    } catch (error) {
      console.error('登出失败:', error)
      // 即使登出请求失败，也要清除本地状态
      user.value = null
    } finally {
      isLoading.value = false
    }
  }

  // 验证token
  const verifyToken = async () => {
    if (!authService.getToken()) {
      return false
    }
    
    try {
      const userData = await authService.verifyToken()
      user.value = userData
      return true
    } catch (error) {
      user.value = null
      return false
    }
  }

  // 刷新用户信息
  const refreshUser = () => {
    const currentUser = authService.getCurrentUser()
    user.value = currentUser
  }

  return {
    // 状态
    user: computed(() => user.value),
    isAuthenticated,
    isLoading: computed(() => isLoading.value),
    
    // 方法
    login,
    register,
    logout,
    verifyToken,
    refreshUser
  }
}
